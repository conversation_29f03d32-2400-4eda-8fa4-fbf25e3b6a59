# إعدادات Apache لنظام إدارة المهام
# Apache Configuration for Task Management System

# تفعيل إعادة الكتابة
RewriteEngine On

# إعدادات الأمان
# Security Settings

# منع الوصول للملفات الحساسة
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf)$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# منع عرض محتويات المجلدات
Options -Indexes

# حماية ملفات الإعدادات
<Files "config/*">
    Order Deny,Allow
    Deny from all
</Files>

# السماح بالوصول لملفات PHP في config عبر include فقط
<Files "config/*.php">
    Order Deny,Allow
    Deny from all
</Files>

# إعدادات PHP
# PHP Settings

# تحديد حجم الملفات المرفوعة
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value memory_limit 256M

# تفعيل عرض الأخطاء في بيئة التطوير
php_value display_errors On
php_value error_reporting E_ALL

# إعدادات الجلسة
php_value session.cookie_httponly 1
php_value session.use_only_cookies 1
php_value session.cookie_secure 0

# إعدادات التخزين المؤقت
# Caching Settings

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
</IfModule>

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# إعدادات الأمان الإضافية
# Additional Security Settings

# منع الوصول المباشر لملفات PHP في مجلدات معينة
<Directory "includes/">
    <Files "*.php">
        Order Deny,Allow
        Deny from all
    </Files>
</Directory>

# حماية من هجمات XSS
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# إعادة التوجيه للصفحة الرئيسية
# Redirect to main page

# إعادة توجيه المجلد الجذر لصفحة تسجيل الدخول
DirectoryIndex index.php

# إعدادات خاصة بالمنفذ 8080
# Port 8080 specific settings

# لا توجد إعدادات خاصة مطلوبة للمنفذ
# المنفذ يتم تحديده في إعدادات Apache أو خادم PHP

# إعادة كتابة URLs للصفحات الودية
# URL Rewriting for friendly URLs

# إعادة توجيه الصفحات القديمة (إذا وجدت)
# Redirect old pages (if any)

# RewriteRule ^login$ index.php [L]
# RewriteRule ^dashboard$ admin/dashboard.php [L]

# معالجة الأخطاء
# Error Handling

# صفحات الأخطاء المخصصة
ErrorDocument 404 /Task/404.php
ErrorDocument 403 /Task/403.php
ErrorDocument 500 /Task/500.php

# إعدادات MIME
# MIME Settings

AddType application/javascript .js
AddType text/css .css
AddType image/svg+xml .svg

# منع الوصول للملفات المخفية
<FilesMatch "^\.">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# السماح بالوصول لملفات الخطوط
<FilesMatch "\.(ttf|otf|eot|woff|woff2)$">
    <IfModule mod_headers.c>
        Header set Access-Control-Allow-Origin "*"
    </IfModule>
</FilesMatch>
