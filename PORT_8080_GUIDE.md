# 🚀 دليل تشغيل النظام على المنفذ 8080

## 📋 نظرة عامة
تم تكوين نظام إدارة المهام للعمل على المنفذ 8080 باستخدام خادم PHP المدمج أو Apache.

## ⚡ التشغيل السريع

### 🎯 الطريقة الأسهل
```bash
# شغل الملف التالي:
run_on_8080.bat

# سيفتح الموقع تلقائياً على:
http://localhost:8080/
```

## 🔧 طرق التشغيل المختلفة

### 1️⃣ خادم PHP المدمج (الموصى به)
```bash
# من سطر الأوامر:
php -S localhost:8080

# أو شغل:
run_on_8080.bat
```

### 2️⃣ Apache مع XAMPP
```bash
# شغل ملف الإعداد:
setup_port_8080.bat

# ثم أعد تشغيل Apache من لوحة تحكم XAMPP
```

### 3️⃣ Apache يدوياً
1. افتح ملف: `C:\xampp\apache\conf\httpd.conf`
2. غير `Listen 80` إلى `Listen 8080`
3. غير `ServerName localhost:80` إلى `ServerName localhost:8080`
4. أعد تشغيل Apache

## 🌐 الروابط المهمة

| الصفحة | الرابط |
|---------|---------|
| **الصفحة الرئيسية** | http://localhost:8080/ |
| **تسجيل الدخول** | http://localhost:8080/ |
| **إعداد النظام** | http://localhost:8080/setup.php |
| **تحديث قاعدة البيانات** | http://localhost:8080/update_database.php |
| **لوحة تحكم المدير** | http://localhost:8080/admin/dashboard.php |

## 🔑 الحسابات التجريبية

| الدور | اسم المستخدم | كلمة المرور | الوصف |
|-------|---------------|-------------|--------|
| 👨‍💼 **مدير** | `admin` | `password123` | صلاحية كاملة للنظام |
| 👨‍💼 **مشرف** | `supervisor1` | `password123` | إشراف ومتابعة المهام |
| 👨‍💻 **موظف** | `user1` | `password123` | تنفيذ المهام |
| 👨‍💻 **موظف** | `user2` | `password123` | تنفيذ المهام |

## 🛠️ استكشاف الأخطاء

### ❌ المنفذ 8080 مستخدم
```bash
# تحقق من العمليات المستخدمة للمنفذ:
netstat -an | findstr :8080

# أو استخدم منفذ آخر:
php -S localhost:8081
```

### ❌ PHP غير موجود
```bash
# تأكد من تثبيت PHP:
php --version

# أو استخدم PHP من XAMPP:
C:\xampp\php\php.exe -S localhost:8080
```

### ❌ خطأ في قاعدة البيانات
1. تأكد من تشغيل MySQL
2. انتقل إلى: http://localhost:8080/setup.php
3. أو: http://localhost:8080/update_database.php

## 📁 الملفات المهمة للمنفذ 8080

```
Task/
├── run_on_8080.bat           # تشغيل سريع على المنفذ 8080
├── setup_port_8080.bat       # إعداد Apache للمنفذ 8080
├── start_server_8080.bat     # تشغيل خادم PHP
├── .htaccess                 # إعدادات Apache
└── config/
    └── database.php          # محدث للمنفذ 8080
```

## 🔒 الأمان

### إعدادات الأمان المطبقة:
- ✅ تشفير كلمات المرور (bcrypt)
- ✅ حماية CSRF
- ✅ تنظيف البيانات المدخلة
- ✅ حماية الملفات الحساسة
- ✅ منع عرض محتويات المجلدات
- ✅ تحديد حجم الملفات المرفوعة

## 📊 مراقبة الأداء

### فحص حالة الخادم:
```bash
# فحص المنفذ:
netstat -an | findstr :8080

# فحص العمليات:
tasklist | findstr php
```

## 🚀 التحسينات المطبقة

### للمنفذ 8080:
- ⚡ خادم PHP مدمج سريع
- 🔧 إعداد تلقائي للمنفذ
- 🌐 فتح المتصفح تلقائياً
- 📝 عرض معلومات الحسابات
- 🛡️ إعدادات أمان محسنة

## 📞 الدعم

### في حالة المشاكل:
1. **تحقق من سجلات الأخطاء**
2. **تأكد من تشغيل MySQL**
3. **تحقق من صلاحيات الملفات**
4. **راجع ملف .htaccess**

### الملفات المساعدة:
- `run_on_8080.bat` - للتشغيل السريع
- `setup_port_8080.bat` - لإعداد Apache
- `PORT_8080_GUIDE.md` - هذا الدليل

## 🎯 نصائح مهمة

### للتطوير:
- استخدم `run_on_8080.bat` للتشغيل السريع
- المنفذ 8080 لا يتعارض مع خدمات أخرى عادة
- خادم PHP المدمج مثالي للتطوير والاختبار

### للإنتاج:
- استخدم Apache أو Nginx
- فعل HTTPS
- استخدم قاعدة بيانات منفصلة
- فعل النسخ الاحتياطي التلقائي

---

**✨ النظام جاهز للاستخدام على المنفذ 8080! ✨**

للبدء: شغل `run_on_8080.bat` وانتقل إلى http://localhost:8080/
