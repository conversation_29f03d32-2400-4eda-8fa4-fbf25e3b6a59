# نظام إدارة المهام للشركات 🚀

نظام إدارة مهام حديث ومتطور مصمم خصيصاً للشركات العربية، يوفر واجهة مستخدم عصرية وتجربة استخدام سلسة.

## ✨ المميزات الرئيسية

### 🔐 نظام الأدوار والصلاحيات
- **المدير (Admin)**: صلاحية كاملة لإدارة النظام وإنشاء المهام
- **المشرف (Supervisor)**: متابعة المهام والإشراف على الموظفين
- **الموظف (User)**: استقبال المهام وتحديث حالة التنفيذ

### 📋 إدارة المهام المتقدمة
- إنشاء وتوزيع المهام بسهولة
- تتبع حالة المهام (معلقة، قيد التنفيذ، مكتملة، متأخرة)
- تحديد الأولويات والمواعيد النهائية
- إضافة المرفقات والملاحظات
- نظام إشعارات فوري

### 🎨 تصميم حديث ومتجاوب
- واجهة مستخدم عصرية تناسب عام 2026
- تصميم متجاوب يعمل على جميع الأجهزة
- ألوان وتأثيرات بصرية جذابة
- تجربة مستخدم سلسة ومريحة

## 🛠️ التقنيات المستخدمة

- **Backend**: PHP 8+ مع PDO
- **Frontend**: HTML5, CSS3, JavaScript ES6+
- **Database**: MySQL 8.0+
- **Styling**: CSS Grid, Flexbox, Custom CSS
- **Icons**: Font Awesome 6.5
- **Charts**: Chart.js

## 📦 متطلبات التشغيل

- PHP 8.0 أو أحدث
- MySQL 8.0 أو أحدث
- Apache/Nginx Web Server
- XAMPP/WAMP/LAMP (للتطوير المحلي)

## 🚀 طريقة التثبيت

### 1. تحضير البيئة
```bash
# تأكد من تشغيل XAMPP
# تفعيل Apache و MySQL
```

### 2. نسخ الملفات
```bash
# انسخ جميع ملفات المشروع إلى مجلد htdocs
# المسار: d:\xampp\htdocs\Task
```

### 3. إعداد قاعدة البيانات
```sql
-- افتح phpMyAdmin أو MySQL Command Line
-- قم بتشغيل ملف config/init_database.sql
-- أو استخدم الأوامر التالية:

SOURCE d:/xampp/htdocs/Task/config/init_database.sql;
```

### 4. تكوين الإعدادات
```php
// تحقق من إعدادات قاعدة البيانات في config/database.php
$host = 'localhost';
$db_name = 'task_management_system';
$username = 'root';
$password = '';
```

### 5. تشغيل النظام

#### الطريقة الأولى - خادم PHP المدمج (المنفذ 8080):
```bash
# شغل ملف run_on_8080.bat
# سيفتح الموقع تلقائياً على:
http://localhost:8080/
```

#### الطريقة الثانية - XAMPP (المنفذ 80):
```bash
# شغل ملف start.bat
# أو افتح المتصفح وانتقل إلى:
http://localhost/Task
```

## 👥 الحسابات التجريبية

| الدور | اسم المستخدم | كلمة المرور | الوصف |
|-------|---------------|-------------|--------|
| مدير | admin | password123 | صلاحية كاملة |
| مشرف | supervisor1 | password123 | إشراف ومتابعة |
| موظف | user1 | password123 | تنفيذ المهام |
| موظف | user2 | password123 | تنفيذ المهام |

## 📁 هيكل المشروع

```
Task/
├── config/                 # إعدادات النظام
│   ├── database.php        # إعدادات قاعدة البيانات
│   └── init_database.sql   # هيكل قاعدة البيانات
├── includes/               # ملفات PHP المشتركة
│   └── functions.php       # الدوال المساعدة
├── assets/                 # الملفات الثابتة
│   ├── css/               # ملفات التصميم
│   ├── js/                # ملفات JavaScript
│   └── images/            # الصور
├── admin/                  # لوحة تحكم المدير
│   └── dashboard.php       # الصفحة الرئيسية للمدير
├── supervisor/             # واجهة المشرف
├── user/                   # واجهة الموظف
├── auth/                   # نظام المصادقة
│   ├── login_process.php   # معالجة تسجيل الدخول
│   └── logout.php          # تسجيل الخروج
├── uploads/                # ملفات المرفقات
├── index.php               # صفحة تسجيل الدخول
└── README.md               # دليل الاستخدام
```

## 🔧 الإعدادات المتقدمة

### تخصيص الألوان
```css
/* في assets/css/login.css أو dashboard.css */
:root {
    --primary-color: #667eea;    /* اللون الأساسي */
    --secondary-color: #764ba2;  /* اللون الثانوي */
    --accent-color: #f093fb;     /* لون التمييز */
}
```

### إعدادات الأمان
```php
// في config/database.php
define('ENABLE_2FA', false);           // تفعيل المصادقة الثنائية
define('SESSION_TIMEOUT', 3600);      // انتهاء الجلسة (ثانية)
define('MAX_LOGIN_ATTEMPTS', 5);      // عدد محاولات الدخول
```

## 🔒 الأمان

- تشفير كلمات المرور باستخدام bcrypt
- حماية من هجمات CSRF
- تنظيف البيانات المدخلة
- جلسات آمنة مع انتهاء صلاحية
- تسجيل محاولات الدخول الفاشلة

## 📊 المميزات المستقبلية

- [ ] تطبيق الهاتف المحمول
- [ ] نظام التقارير المتقدم
- [ ] تكامل مع البريد الإلكتروني
- [ ] نظام الدردشة المباشرة
- [ ] تصدير البيانات (PDF, Excel)
- [ ] نظام النسخ الاحتياطي التلقائي

## 🐛 الإبلاغ عن المشاكل

إذا واجهت أي مشاكل، يرجى التحقق من:

1. **إعدادات قاعدة البيانات**: تأكد من صحة بيانات الاتصال
2. **صلاحيات الملفات**: تأكد من صلاحيات الكتابة لمجلد uploads
3. **إصدار PHP**: تأكد من استخدام PHP 8.0 أو أحدث
4. **سجلات الأخطاء**: تحقق من ملفات سجل Apache/PHP

## 📞 الدعم الفني

للحصول على الدعم الفني أو الاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966 XX XXX XXXX

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

---

**تم تطوير هذا النظام بعناية فائقة ليوفر تجربة استخدام متميزة ومتطورة تناسب احتياجات الشركات الحديثة** ✨
