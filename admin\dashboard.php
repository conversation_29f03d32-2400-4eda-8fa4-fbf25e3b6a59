<?php
/**
 * لوحة تحكم المدير
 * Admin Dashboard
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحية
if (!isLoggedIn() || !hasRole('admin')) {
    redirect('../index.php');
}

// الحصول على إحصائيات النظام
$database = new Database();
$conn = $database->getConnection();

// إحصائيات المهام
$taskStats = [];
$taskQuery = "SELECT
    COUNT(*) as total_tasks,
    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_tasks,
    SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_tasks,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_tasks,
    SUM(CASE WHEN status = 'overdue' THEN 1 ELSE 0 END) as overdue_tasks
FROM tasks";
$taskStmt = $conn->prepare($taskQuery);
$taskStmt->execute();
$taskStats = $taskStmt->fetch();

// إحصائيات المستخدمين
$userStats = [];
$userQuery = "SELECT
    COUNT(*) as total_users,
    SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admin_users,
    SUM(CASE WHEN role = 'supervisor' THEN 1 ELSE 0 END) as supervisor_users,
    SUM(CASE WHEN role = 'user' THEN 1 ELSE 0 END) as regular_users,
    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_users
FROM users";
$userStmt = $conn->prepare($userQuery);
$userStmt->execute();
$userStats = $userStmt->fetch();

// المهام الحديثة
$recentTasks = [];
$recentQuery = "SELECT t.*, u1.full_name as assigned_to_name, u2.full_name as assigned_by_name
                FROM tasks t
                LEFT JOIN users u1 ON t.assigned_to = u1.id
                LEFT JOIN users u2 ON t.assigned_by = u2.id
                ORDER BY t.created_at DESC
                LIMIT 5";
$recentStmt = $conn->prepare($recentQuery);
$recentStmt->execute();
$recentTasks = $recentStmt->fetchAll();

// الإشعارات غير المقروءة
$notificationQuery = "SELECT COUNT(*) as unread_count FROM notifications WHERE user_id = ? AND is_read = 0";
$notificationStmt = $conn->prepare($notificationQuery);
$notificationStmt->execute([$_SESSION['user_id']]);
$unreadNotifications = $notificationStmt->fetch()['unread_count'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المدير - <?php echo SITE_NAME; ?></title>

    <!-- CSS Files -->
    <link rel="stylesheet" href="../assets/css/variables.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Sidebar -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-tasks"></i>
                <span>إدارة المهام</span>
            </div>
        </div>

        <nav class="sidebar-nav">
            <ul>
                <li class="active">
                    <a href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </li>
                <li>
                    <a href="tasks.php">
                        <i class="fas fa-tasks"></i>
                        <span>إدارة المهام</span>
                    </a>
                </li>
                <li>
                    <a href="users.php">
                        <i class="fas fa-users"></i>
                        <span>إدارة المستخدمين</span>
                    </a>
                </li>
                <li>
                    <a href="reports.php">
                        <i class="fas fa-chart-bar"></i>
                        <span>التقارير</span>
                    </a>
                </li>
                <li>
                    <a href="settings.php">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </a>
                </li>
            </ul>
        </nav>

        <div class="sidebar-footer">
            <a href="../auth/logout.php" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                <span>تسجيل الخروج</span>
            </a>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <button class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>لوحة تحكم المدير</h1>
            </div>

            <div class="header-right">
                <div class="notifications">
                    <button class="notification-btn">
                        <i class="fas fa-bell"></i>
                        <?php if ($unreadNotifications > 0): ?>
                            <span class="notification-badge"><?php echo $unreadNotifications; ?></span>
                        <?php endif; ?>
                    </button>
                </div>

                <div class="user-menu">
                    <div class="user-info">
                        <span class="user-name"><?php echo htmlspecialchars($_SESSION['full_name']); ?></span>
                        <span class="user-role">مدير النظام</span>
                    </div>
                    <div class="user-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon tasks">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $taskStats['total_tasks']; ?></h3>
                        <p>إجمالي المهام</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon users">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $userStats['active_users']; ?></h3>
                        <p>المستخدمين النشطين</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon completed">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $taskStats['completed_tasks']; ?></h3>
                        <p>المهام المكتملة</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon pending">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $taskStats['pending_tasks']; ?></h3>
                        <p>المهام المعلقة</p>
                    </div>
                </div>
            </div>

            <!-- Charts and Recent Tasks -->
            <div class="dashboard-grid">
                <!-- Task Status Chart -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>توزيع حالات المهام</h3>
                    </div>
                    <div class="card-content">
                        <canvas id="taskStatusChart"></canvas>
                    </div>
                </div>

                <!-- Recent Tasks -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>المهام الحديثة</h3>
                        <a href="tasks.php" class="view-all">عرض الكل</a>
                    </div>
                    <div class="card-content">
                        <div class="task-list">
                            <?php foreach ($recentTasks as $task): ?>
                                <div class="task-item">
                                    <div class="task-info">
                                        <h4><?php echo htmlspecialchars($task['title']); ?></h4>
                                        <p>مُكلف إلى: <?php echo htmlspecialchars($task['assigned_to_name']); ?></p>
                                        <span class="task-date"><?php echo formatDateArabic($task['created_at']); ?></span>
                                    </div>
                                    <div class="task-status">
                                        <span class="status-badge <?php echo getTaskStatusColor($task['status']); ?>">
                                            <?php
                                            $statusLabels = [
                                                'pending' => 'معلقة',
                                                'in_progress' => 'قيد التنفيذ',
                                                'completed' => 'مكتملة',
                                                'overdue' => 'متأخرة'
                                            ];
                                            echo $statusLabels[$task['status']];
                                            ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <h3>الإجراءات السريعة</h3>
                <div class="action-buttons">
                    <a href="create_task.php" class="action-btn primary">
                        <i class="fas fa-plus"></i>
                        <span>إنشاء مهمة جديدة</span>
                    </a>
                    <a href="create_user.php" class="action-btn secondary">
                        <i class="fas fa-user-plus"></i>
                        <span>إضافة مستخدم</span>
                    </a>
                    <a href="reports.php" class="action-btn info">
                        <i class="fas fa-chart-line"></i>
                        <span>عرض التقارير</span>
                    </a>
                    <a href="backup.php" class="action-btn warning">
                        <i class="fas fa-download"></i>
                        <span>نسخ احتياطي</span>
                    </a>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="../assets/js/dashboard.js"></script>
    <script>
        // رسم بياني لحالات المهام
        const ctx = document.getElementById('taskStatusChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['معلقة', 'قيد التنفيذ', 'مكتملة', 'متأخرة'],
                datasets: [{
                    data: [
                        <?php echo $taskStats['pending_tasks']; ?>,
                        <?php echo $taskStats['in_progress_tasks']; ?>,
                        <?php echo $taskStats['completed_tasks']; ?>,
                        <?php echo $taskStats['overdue_tasks']; ?>
                    ],
                    backgroundColor: [
                        '#fbbf24',
                        '#3b82f6',
                        '#10b981',
                        '#ef4444'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    </script>
</body>
</html>
