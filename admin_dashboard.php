<?php
/**
 * لوحة تحكم المدير المبسطة
 * Simplified Admin Dashboard
 */

session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: index_simple.php');
    exit;
}

define('SITE_NAME', 'نظام إدارة المهام');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المدير - <?php echo SITE_NAME; ?></title>
    
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a6fd8;
            --success-color: #10b981;
            --warning-color: #fbbf24;
            --error-color: #ef4444;
            --info-color: #3b82f6;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-500: #6b7280;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --white: #ffffff;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            background: var(--gray-50);
            color: var(--gray-800);
            line-height: 1.6;
        }
        
        .header {
            background: var(--white);
            border-bottom: 1px solid var(--gray-200);
            padding: 1rem 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-name {
            font-weight: 600;
        }
        
        .logout-btn {
            background: var(--error-color);
            color: var(--white);
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }
        
        .logout-btn:hover {
            background: #dc2626;
            transform: translateY(-1px);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .welcome-card {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: var(--white);
            padding: 2rem;
            border-radius: 16px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .welcome-title {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: var(--white);
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-200);
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
        }
        
        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--white);
            margin-bottom: 1rem;
        }
        
        .stat-icon.tasks {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        }
        
        .stat-icon.users {
            background: linear-gradient(135deg, var(--info-color), #1e40af);
        }
        
        .stat-icon.completed {
            background: linear-gradient(135deg, var(--success-color), #059669);
        }
        
        .stat-icon.pending {
            background: linear-gradient(135deg, var(--warning-color), #d97706);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 0.25rem;
        }
        
        .stat-label {
            color: var(--gray-600);
            font-size: 0.875rem;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .action-btn {
            background: var(--white);
            border: 2px solid var(--gray-200);
            padding: 1.5rem;
            border-radius: 12px;
            text-decoration: none;
            color: var(--gray-700);
            text-align: center;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
        }
        
        .action-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-2px);
        }
        
        .action-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .system-info {
            background: var(--white);
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid var(--gray-200);
        }
        
        .system-info h3 {
            margin-bottom: 1rem;
            color: var(--gray-800);
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--gray-100);
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .status-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }
        
        .status-warning {
            background: rgba(251, 191, 36, 0.1);
            color: var(--warning-color);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                📋 <?php echo SITE_NAME; ?>
            </div>
            <div class="user-info">
                <span class="user-name">مرحباً، <?php echo htmlspecialchars($_SESSION['full_name']); ?></span>
                <a href="logout.php" class="logout-btn">تسجيل الخروج</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container">
        <!-- Welcome Card -->
        <div class="welcome-card">
            <h1 class="welcome-title">مرحباً بك في لوحة التحكم</h1>
            <p>إدارة مهام شركتك بكفاءة وسهولة</p>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon tasks">📋</div>
                <div class="stat-number">24</div>
                <div class="stat-label">إجمالي المهام</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon users">👥</div>
                <div class="stat-number">8</div>
                <div class="stat-label">المستخدمين النشطين</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon completed">✅</div>
                <div class="stat-number">18</div>
                <div class="stat-label">المهام المكتملة</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon pending">⏳</div>
                <div class="stat-number">6</div>
                <div class="stat-label">المهام المعلقة</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <h2 style="margin-bottom: 1rem; color: var(--gray-800);">الإجراءات السريعة</h2>
        <div class="actions-grid">
            <a href="#" class="action-btn">
                <div class="action-icon">➕</div>
                <span>إنشاء مهمة جديدة</span>
            </a>
            
            <a href="#" class="action-btn">
                <div class="action-icon">👤</div>
                <span>إضافة مستخدم</span>
            </a>
            
            <a href="#" class="action-btn">
                <div class="action-icon">📊</div>
                <span>عرض التقارير</span>
            </a>
            
            <a href="#" class="action-btn">
                <div class="action-icon">⚙️</div>
                <span>إعدادات النظام</span>
            </a>
            
            <a href="test.php" class="action-btn">
                <div class="action-icon">🧪</div>
                <span>اختبار النظام</span>
            </a>
            
            <a href="debug.php" class="action-btn">
                <div class="action-icon">🔍</div>
                <span>فحص الأخطاء</span>
            </a>
        </div>

        <!-- System Information -->
        <div class="system-info">
            <h3>معلومات النظام</h3>
            <div class="info-item">
                <span>حالة النظام:</span>
                <span class="status-badge status-success">يعمل بشكل طبيعي</span>
            </div>
            <div class="info-item">
                <span>إصدار PHP:</span>
                <span><?php echo PHP_VERSION; ?></span>
            </div>
            <div class="info-item">
                <span>المنفذ:</span>
                <span><?php echo $_SERVER['SERVER_PORT']; ?></span>
            </div>
            <div class="info-item">
                <span>آخر تسجيل دخول:</span>
                <span><?php echo date('Y-m-d H:i:s'); ?></span>
            </div>
            <div class="info-item">
                <span>قاعدة البيانات:</span>
                <span class="status-badge status-warning">تحتاج إعداد</span>
            </div>
        </div>
    </div>
</body>
</html>
