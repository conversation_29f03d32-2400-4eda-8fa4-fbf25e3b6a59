/* مكونات UI المشتركة - Shared UI Components */
/* هذا الملف يحتوي على التصاميم المشتركة لجميع صفحات النظام */

/* الأزرار - Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: var(--border-gray);
}

.btn-secondary:hover {
    background: var(--gray-200);
    transform: translateY(-1px);
}

.btn-success {
    background: var(--gradient-success);
    color: var(--white);
}

.btn-warning {
    background: var(--gradient-warning);
    color: var(--white);
}

.btn-error {
    background: var(--gradient-error);
    color: var(--white);
}

.btn-info {
    background: var(--gradient-info);
    color: var(--white);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--text-sm);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--text-lg);
}

/* البطاقات - Cards */
.card {
    background: var(--white);
    border: var(--border-gray);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: var(--border-gray);
    background: var(--gray-50);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: var(--border-gray);
    background: var(--gray-50);
}

/* النماذج - Forms */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-weight: var(--font-medium);
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    font-size: var(--text-sm);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: var(--text-base);
    transition: var(--transition);
    background: var(--white);
    color: var(--gray-800);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--focus-ring-primary);
}

.form-input.error,
.form-select.error,
.form-textarea.error {
    border-color: var(--error-color);
    box-shadow: var(--focus-ring-error);
}

.form-input.success,
.form-select.success,
.form-textarea.success {
    border-color: var(--success-color);
    box-shadow: var(--focus-ring-success);
}

.form-help {
    font-size: var(--text-xs);
    color: var(--gray-500);
    margin-top: var(--spacing-xs);
}

.form-error {
    font-size: var(--text-xs);
    color: var(--error-color);
    margin-top: var(--spacing-xs);
}

/* التنبيهات - Alerts */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-lg);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.alert-primary {
    background: var(--bg-primary-light);
    color: var(--primary-color);
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-right: 4px solid var(--primary-color);
}

.alert-success {
    background: var(--bg-success-light);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.2);
    border-right: 4px solid var(--success-color);
}

.alert-warning {
    background: var(--bg-warning-light);
    color: var(--warning-color);
    border: 1px solid rgba(251, 191, 36, 0.2);
    border-right: 4px solid var(--warning-color);
}

.alert-error {
    background: var(--bg-error-light);
    color: var(--error-color);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-right: 4px solid var(--error-color);
}

.alert-info {
    background: var(--bg-info-light);
    color: var(--info-color);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-right: 4px solid var(--info-color);
}

/* الشارات - Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-primary {
    background: var(--bg-primary-light);
    color: var(--primary-color);
}

.badge-success {
    background: var(--bg-success-light);
    color: var(--success-color);
}

.badge-warning {
    background: var(--bg-warning-light);
    color: var(--warning-color);
}

.badge-error {
    background: var(--bg-error-light);
    color: var(--error-color);
}

.badge-info {
    background: var(--bg-info-light);
    color: var(--info-color);
}

.badge-gray {
    background: var(--gray-100);
    color: var(--gray-600);
}

/* التحميل - Loading */
.loading {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.spinner {
    width: 1rem;
    height: 1rem;
    border: 2px solid var(--gray-200);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.spinner-sm {
    width: 0.75rem;
    height: 0.75rem;
    border-width: 1px;
}

.spinner-lg {
    width: 1.5rem;
    height: 1.5rem;
    border-width: 3px;
}

/* الجداول - Tables */
.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table th,
.table td {
    padding: var(--spacing-md);
    text-align: right;
    border-bottom: 1px solid var(--gray-200);
}

.table th {
    background: var(--gray-50);
    font-weight: var(--font-semibold);
    color: var(--gray-700);
    font-size: var(--text-sm);
}

.table tbody tr:hover {
    background: var(--gray-50);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* التبويبات - Tabs */
.tabs {
    border-bottom: 1px solid var(--gray-200);
    margin-bottom: var(--spacing-lg);
}

.tab-list {
    display: flex;
    gap: var(--spacing-md);
    list-style: none;
    margin: 0;
    padding: 0;
}

.tab-item {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 2px solid transparent;
    color: var(--gray-600);
    text-decoration: none;
    font-weight: var(--font-medium);
    transition: var(--transition);
    cursor: pointer;
}

.tab-item:hover {
    color: var(--primary-color);
}

.tab-item.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

/* القوائم المنسدلة - Dropdowns */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--white);
    border: var(--border-gray);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    z-index: var(--z-dropdown);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
}

.dropdown.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--gray-700);
    text-decoration: none;
    transition: var(--transition);
}

.dropdown-item:hover {
    background: var(--gray-50);
    color: var(--primary-color);
}

.dropdown-divider {
    height: 1px;
    background: var(--gray-200);
    margin: var(--spacing-xs) 0;
}

/* النوافذ المنبثقة - Modals */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: var(--z-modal-backdrop);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-2xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
    transform: scale(0.9);
    transition: var(--transition);
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: var(--border-gray);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--gray-800);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--text-xl);
    color: var(--gray-400);
    cursor: pointer;
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--gray-600);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: var(--border-gray);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

/* فئات مساعدة - Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-inline-flex { display: inline-flex; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }

.m-0 { margin: 0; }
.mt-auto { margin-top: auto; }
.mb-auto { margin-bottom: auto; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

.select-none { user-select: none; }

.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
