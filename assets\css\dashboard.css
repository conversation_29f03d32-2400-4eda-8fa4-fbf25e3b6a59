/* تصميم لوحة التحكم الحديث */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

:root {
    --primary-color: #667eea;
    --primary-dark: #5a6fd8;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #10b981;
    --warning-color: #fbbf24;
    --error-color: #ef4444;
    --info-color: #3b82f6;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    --sidebar-width: 280px;
    --header-height: 70px;
    --border-radius: 12px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    background-color: var(--gray-50);
    color: var(--gray-800);
    line-height: 1.6;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: linear-gradient(180deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    z-index: 1000;
    transition: var(--transition);
    box-shadow: var(--shadow-lg);
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 700;
}

.logo i {
    font-size: 1.5rem;
    color: var(--accent-color);
}

.sidebar-nav {
    padding: 1rem 0;
    flex: 1;
}

.sidebar-nav ul {
    list-style: none;
}

.sidebar-nav li {
    margin-bottom: 0.25rem;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
    border-radius: 0;
    position: relative;
}

.sidebar-nav a:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
}

.sidebar-nav li.active a {
    background: rgba(255, 255, 255, 0.15);
    color: var(--white);
}

.sidebar-nav li.active a::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--accent-color);
}

.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.logout-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
}

/* Main Content */
.main-content {
    margin-right: var(--sidebar-width);
    min-height: 100vh;
    transition: var(--transition);
}

/* Header */
.header {
    height: var(--header-height);
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--gray-600);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background: var(--gray-100);
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-800);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.notification-btn {
    position: relative;
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--gray-600);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.notification-btn:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.notification-badge {
    position: absolute;
    top: 0;
    left: 0;
    background: var(--error-color);
    color: var(--white);
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 50px;
    min-width: 1.25rem;
    text-align: center;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.user-menu:hover {
    background: var(--gray-100);
}

.user-info {
    text-align: left;
}

.user-name {
    display: block;
    font-weight: 600;
    color: var(--gray-800);
    font-size: 0.875rem;
}

.user-role {
    display: block;
    color: var(--gray-500);
    font-size: 0.75rem;
}

.user-avatar {
    font-size: 2rem;
    color: var(--primary-color);
}

/* Dashboard Content */
.dashboard-content {
    padding: 2rem;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
}

.stat-icon.tasks {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.stat-icon.users {
    background: linear-gradient(135deg, var(--info-color), #1e40af);
}

.stat-icon.completed {
    background: linear-gradient(135deg, var(--success-color), #059669);
}

.stat-icon.pending {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 0.25rem;
}

.stat-info p {
    color: var(--gray-600);
    font-size: 0.875rem;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
}

.view-all {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition);
}

.view-all:hover {
    color: var(--primary-dark);
}

.card-content {
    padding: 1.5rem;
}

/* Task List */
.task-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.task-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.task-item:hover {
    background: var(--gray-100);
}

.task-info h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.25rem;
}

.task-info p {
    font-size: 0.75rem;
    color: var(--gray-600);
    margin-bottom: 0.25rem;
}

.task-date {
    font-size: 0.75rem;
    color: var(--gray-500);
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.warning {
    background: rgba(251, 191, 36, 0.1);
    color: var(--warning-color);
}

.status-badge.info {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.status-badge.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-badge.danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

/* Quick Actions */
.quick-actions {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.quick-actions h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1rem;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    border: 2px solid transparent;
}

.action-btn.primary {
    background: var(--primary-color);
    color: var(--white);
}

.action-btn.primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.action-btn.secondary {
    background: var(--gray-100);
    color: var(--gray-700);
}

.action-btn.secondary:hover {
    background: var(--gray-200);
    transform: translateY(-2px);
}

.action-btn.info {
    background: var(--info-color);
    color: var(--white);
}

.action-btn.info:hover {
    background: #1e40af;
    transform: translateY(-2px);
}

.action-btn.warning {
    background: var(--warning-color);
    color: var(--white);
}

.action-btn.warning:hover {
    background: #d97706;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .sidebar-toggle {
        display: block;
    }
    
    .header {
        padding: 0 1rem;
    }
    
    .dashboard-content {
        padding: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
    
    .user-info {
        display: none;
    }
}
