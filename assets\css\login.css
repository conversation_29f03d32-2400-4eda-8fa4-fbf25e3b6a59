/* تصميم صفحة تسجيل الدخول المتناسق مع النظام 2026 */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

:root {
    /* نفس متغيرات لوحة التحكم للتناسق */
    --primary-color: #667eea;
    --primary-dark: #5a6fd8;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #10b981;
    --warning-color: #fbbf24;
    --error-color: #ef4444;
    --info-color: #3b82f6;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    --border-radius: 12px;
    --border-radius-lg: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    background: var(--gray-50);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    color: var(--gray-800);
    line-height: 1.6;
}

/* خلفية متدرجة أنيقة */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, var(--secondary-color) 100%);
    opacity: 0.03;
    z-index: -2;
}

/* نمط هندسي في الخلفية */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, var(--primary-color) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, var(--secondary-color) 0%, transparent 50%),
        radial-gradient(circle at 75% 25%, var(--accent-color) 0%, transparent 50%),
        radial-gradient(circle at 25% 75%, var(--info-color) 0%, transparent 50%);
    opacity: 0.02;
    z-index: -1;
    animation: backgroundShift 30s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(180deg); }
}

.login-container {
    position: relative;
    z-index: 1;
    width: 100%;
    max-width: 450px;
    margin: 0 20px;
}

.login-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: 3rem 2.5rem;
    box-shadow: var(--shadow-xl);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
}

.login-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

.login-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.logo {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.logo::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s;
}

.logo:hover::before {
    left: 100%;
}

.logo i {
    font-size: 2rem;
    color: var(--white);
    position: relative;
    z-index: 1;
}

.login-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.login-subtitle {
    color: var(--gray-500);
    font-size: 1rem;
    font-weight: 400;
}

.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-label {
    display: block;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-input {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 3rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background: var(--white);
    color: var(--gray-800);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.input-icon {
    position: absolute;
    top: 50%;
    right: 1rem;
    transform: translateY(-50%);
    color: var(--gray-400);
    font-size: 1.125rem;
    transition: var(--transition);
}

.form-input:focus + .input-icon {
    color: var(--primary-color);
}

.password-toggle {
    position: absolute;
    top: 50%;
    left: 1rem;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    font-size: 1.125rem;
    transition: var(--transition);
}

.password-toggle:hover {
    color: var(--primary-color);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    font-size: 0.875rem;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.remember-me input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    accent-color: var(--primary-color);
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.forgot-password:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.login-btn {
    width: 100%;
    padding: 0.875rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.login-btn:hover::before {
    left: 100%;
}

.alert {
    padding: 0.875rem 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-right: 4px solid var(--error-color);
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.2);
    border-right: 4px solid var(--success-color);
}

.loading {
    display: none;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.spinner {
    width: 1rem;
    height: 1rem;
    border: 2px solid rgba(255,255,255,0.3);
    border-top: 2px solid var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تصميم متجاوب */
@media (max-width: 480px) {
    .login-card {
        padding: 2rem 1.5rem;
        margin: 1rem;
    }

    .login-title {
        font-size: 1.5rem;
    }

    .form-options {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}

/* تأثيرات إضافية */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات إضافية للتناسق */
.login-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--gray-200);
    color: var(--gray-500);
    font-size: 0.875rem;
}

.login-footer p {
    margin-bottom: 0.5rem;
}

.login-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.login-footer a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* تحسين شكل الحقول */
.form-input::placeholder {
    color: var(--gray-400);
    font-size: 0.875rem;
}

.form-group.focused .form-label {
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* تحسين أزرار الإجراءات */
.demo-accounts {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: 1rem;
    box-shadow: var(--shadow-lg);
    font-size: 0.75rem;
    max-width: 300px;
    z-index: 1000;
    transition: var(--transition);
}

.demo-accounts:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.demo-accounts h4 {
    color: var(--warning-color);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.demo-accounts .account-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
    padding: 0.25rem 0;
    border-bottom: 1px solid var(--gray-100);
}

.demo-accounts .account-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.demo-accounts .role {
    font-weight: 600;
    color: var(--gray-700);
}

.demo-accounts .credentials {
    color: var(--gray-500);
    font-family: 'Courier New', monospace;
}

/* تحسين الاستجابة */
@media (max-width: 640px) {
    .demo-accounts {
        position: relative;
        bottom: auto;
        left: auto;
        margin-top: 2rem;
        max-width: 100%;
    }

    .login-container {
        margin: 1rem;
        max-width: 100%;
    }
}
