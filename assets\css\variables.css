/* متغيرات CSS المشتركة لضمان التناسق في جميع أنحاء النظام */
/* Shared CSS Variables for System-wide Consistency */

:root {
    /* الألوان الأساسية - Primary Colors */
    --primary-color: #667eea;
    --primary-dark: #5a6fd8;
    --primary-light: #818cf8;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    
    /* ألوان الحالة - Status Colors */
    --success-color: #10b981;
    --warning-color: #fbbf24;
    --error-color: #ef4444;
    --info-color: #3b82f6;
    
    /* الألوان المحايدة - Neutral Colors */
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --white: #ffffff;
    --black: #000000;
    
    /* تدرجات الرمادي - Gray Scale */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* الظلال - Shadows */
    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    
    /* الحدود المستديرة - Border Radius */
    --border-radius-sm: 6px;
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 20px;
    --border-radius-full: 9999px;
    
    /* الانتقالات - Transitions */
    --transition-fast: all 0.15s ease;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* المسافات - Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* أحجام الخط - Font Sizes */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    
    /* أوزان الخط - Font Weights */
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    
    /* ارتفاعات الخط - Line Heights */
    --leading-tight: 1.25;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;
    
    /* أبعاد التخطيط - Layout Dimensions */
    --sidebar-width: 280px;
    --sidebar-width-collapsed: 80px;
    --header-height: 70px;
    --footer-height: 60px;
    
    /* نقاط الكسر - Breakpoints */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
    
    /* Z-Index Layers */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
    
    /* تدرجات مخصصة - Custom Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    --gradient-success: linear-gradient(135deg, var(--success-color), #059669);
    --gradient-warning: linear-gradient(135deg, var(--warning-color), #d97706);
    --gradient-error: linear-gradient(135deg, var(--error-color), #dc2626);
    --gradient-info: linear-gradient(135deg, var(--info-color), #1e40af);
    
    /* خلفيات شفافة - Transparent Backgrounds */
    --bg-primary-light: rgba(102, 126, 234, 0.1);
    --bg-secondary-light: rgba(118, 75, 162, 0.1);
    --bg-success-light: rgba(16, 185, 129, 0.1);
    --bg-warning-light: rgba(251, 191, 36, 0.1);
    --bg-error-light: rgba(239, 68, 68, 0.1);
    --bg-info-light: rgba(59, 130, 246, 0.1);
    
    /* حدود ملونة - Colored Borders */
    --border-primary: 1px solid var(--primary-color);
    --border-secondary: 1px solid var(--secondary-color);
    --border-success: 1px solid var(--success-color);
    --border-warning: 1px solid var(--warning-color);
    --border-error: 1px solid var(--error-color);
    --border-info: 1px solid var(--info-color);
    --border-gray: 1px solid var(--gray-200);
    
    /* تأثيرات التركيز - Focus Effects */
    --focus-ring-primary: 0 0 0 3px rgba(102, 126, 234, 0.1);
    --focus-ring-success: 0 0 0 3px rgba(16, 185, 129, 0.1);
    --focus-ring-warning: 0 0 0 3px rgba(251, 191, 36, 0.1);
    --focus-ring-error: 0 0 0 3px rgba(239, 68, 68, 0.1);
    
    /* متغيرات الوضع المظلم - Dark Mode Variables */
    --dark-bg-primary: #1a202c;
    --dark-bg-secondary: #2d3748;
    --dark-text-primary: #f7fafc;
    --dark-text-secondary: #e2e8f0;
    --dark-border: #4a5568;
}

/* فئات مساعدة للألوان - Color Utility Classes */
.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }
.text-info { color: var(--info-color); }

.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-error { background-color: var(--error-color); }
.bg-info { background-color: var(--info-color); }

/* فئات مساعدة للظلال - Shadow Utility Classes */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }

/* فئات مساعدة للحدود المستديرة - Border Radius Utility Classes */
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded { border-radius: var(--border-radius); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-xl { border-radius: var(--border-radius-xl); }
.rounded-full { border-radius: var(--border-radius-full); }

/* فئات مساعدة للانتقالات - Transition Utility Classes */
.transition-fast { transition: var(--transition-fast); }
.transition { transition: var(--transition); }
.transition-slow { transition: var(--transition-slow); }

/* إعدادات الخط العربي - Arabic Font Settings */
.font-arabic {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

/* تحسينات للنصوص العربية - Arabic Text Enhancements */
.arabic-text {
    font-feature-settings: "liga" 1, "calt" 1, "kern" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* متغيرات مخصصة للوضع المظلم - Dark Mode Custom Variables */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: var(--dark-bg-primary);
        --bg-secondary: var(--dark-bg-secondary);
        --text-primary: var(--dark-text-primary);
        --text-secondary: var(--dark-text-secondary);
        --border-color: var(--dark-border);
    }
}

/* تحسينات الأداء - Performance Optimizations */
* {
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

/* تحسين التمرير - Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* إخفاء شريط التمرير في Webkit */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* تحسين التركيز للوصولية - Focus Improvements for Accessibility */
:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* إزالة التركيز الافتراضي */
:focus:not(:focus-visible) {
    outline: none;
}
