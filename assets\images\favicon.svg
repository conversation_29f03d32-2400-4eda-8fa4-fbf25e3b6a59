<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="50" cy="50" r="45" fill="url(#grad1)" stroke="#ffffff" stroke-width="2"/>
  
  <!-- Task Icon -->
  <g fill="#ffffff" stroke="#ffffff" stroke-width="1">
    <!-- Checklist Lines -->
    <rect x="25" y="25" width="20" height="3" rx="1.5"/>
    <rect x="25" y="35" width="25" height="3" rx="1.5"/>
    <rect x="25" y="45" width="15" height="3" rx="1.5"/>
    <rect x="25" y="55" width="30" height="3" rx="1.5"/>
    <rect x="25" y="65" width="20" height="3" rx="1.5"/>
    
    <!-- Checkmarks -->
    <path d="M15 27 L18 30 L23 22" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M15 47 L18 50 L23 42" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M15 67 L18 70 L23 62" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    
    <!-- Pending circles -->
    <circle cx="18" cy="37" r="2" fill="none" stroke="#ffffff" stroke-width="1.5"/>
    <circle cx="18" cy="57" r="2" fill="none" stroke="#ffffff" stroke-width="1.5"/>
  </g>
</svg>
