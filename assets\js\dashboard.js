/**
 * JavaScript للوحة التحكم
 * Dashboard JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // عناصر الواجهة
    const sidebar = document.querySelector('.sidebar');
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const mainContent = document.querySelector('.main-content');
    const notificationBtn = document.querySelector('.notification-btn');
    const userMenu = document.querySelector('.user-menu');

    // تبديل الشريط الجانبي في الشاشات الصغيرة
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
            
            // إضافة overlay للشاشات الصغيرة
            if (sidebar.classList.contains('active')) {
                createOverlay();
            } else {
                removeOverlay();
            }
        });
    }

    // إنشاء overlay
    function createOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'sidebar-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        document.body.appendChild(overlay);
        
        // تأثير الظهور
        setTimeout(() => {
            overlay.style.opacity = '1';
        }, 10);
        
        // إغلاق الشريط الجانبي عند النقر على overlay
        overlay.addEventListener('click', function() {
            sidebar.classList.remove('active');
            removeOverlay();
        });
    }

    // إزالة overlay
    function removeOverlay() {
        const overlay = document.querySelector('.sidebar-overlay');
        if (overlay) {
            overlay.style.opacity = '0';
            setTimeout(() => {
                overlay.remove();
            }, 300);
        }
    }

    // إغلاق الشريط الجانبي عند تغيير حجم الشاشة
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            sidebar.classList.remove('active');
            removeOverlay();
        }
    });

    // تفعيل الرابط النشط في الشريط الجانبي
    const currentPage = window.location.pathname.split('/').pop();
    const sidebarLinks = document.querySelectorAll('.sidebar-nav a');
    
    sidebarLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href === currentPage || (currentPage === '' && href === 'dashboard.php')) {
            link.parentElement.classList.add('active');
        } else {
            link.parentElement.classList.remove('active');
        }
    });

    // تأثيرات hover للبطاقات
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // تحديث الوقت الحالي
    function updateCurrentTime() {
        const now = new Date();
        const timeString = now.toLocaleString('ar-EG', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
        
        // إضافة عنصر الوقت إذا لم يكن موجوداً
        let timeElement = document.querySelector('.current-time');
        if (!timeElement) {
            timeElement = document.createElement('div');
            timeElement.className = 'current-time';
            timeElement.style.cssText = `
                position: fixed;
                bottom: 20px;
                left: 20px;
                background: var(--white);
                padding: 0.75rem 1rem;
                border-radius: var(--border-radius);
                box-shadow: var(--shadow-md);
                font-size: 0.875rem;
                color: var(--gray-600);
                z-index: 1000;
            `;
            document.body.appendChild(timeElement);
        }
        
        timeElement.textContent = timeString;
    }

    // تحديث الوقت كل دقيقة
    updateCurrentTime();
    setInterval(updateCurrentTime, 60000);

    // تحديث الإحصائيات تلقائياً
    function refreshStats() {
        fetch('api/get_stats.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث أرقام الإحصائيات
                    updateStatCard('total_tasks', data.stats.total_tasks);
                    updateStatCard('active_users', data.stats.active_users);
                    updateStatCard('completed_tasks', data.stats.completed_tasks);
                    updateStatCard('pending_tasks', data.stats.pending_tasks);
                }
            })
            .catch(error => {
                console.error('Error refreshing stats:', error);
            });
    }

    // تحديث بطاقة إحصائية
    function updateStatCard(type, value) {
        const cards = document.querySelectorAll('.stat-card');
        cards.forEach(card => {
            const icon = card.querySelector('.stat-icon');
            if (icon.classList.contains(type.split('_')[0]) || 
                (type === 'active_users' && icon.classList.contains('users'))) {
                const h3 = card.querySelector('h3');
                if (h3) {
                    // تأثير العد التصاعدي
                    animateNumber(h3, parseInt(h3.textContent), value);
                }
            }
        });
    }

    // تأثير العد التصاعدي للأرقام
    function animateNumber(element, start, end) {
        const duration = 1000;
        const startTime = performance.now();
        
        function update(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const current = Math.floor(start + (end - start) * progress);
            element.textContent = current;
            
            if (progress < 1) {
                requestAnimationFrame(update);
            }
        }
        
        requestAnimationFrame(update);
    }

    // تحديث الإحصائيات كل 5 دقائق
    setInterval(refreshStats, 5 * 60 * 1000);

    // معالجة الإشعارات
    if (notificationBtn) {
        notificationBtn.addEventListener('click', function() {
            toggleNotificationPanel();
        });
    }

    // تبديل لوحة الإشعارات
    function toggleNotificationPanel() {
        let panel = document.querySelector('.notification-panel');
        
        if (panel) {
            panel.remove();
            return;
        }
        
        // إنشاء لوحة الإشعارات
        panel = document.createElement('div');
        panel.className = 'notification-panel';
        panel.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            width: 350px;
            max-height: 400px;
            background: var(--white);
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            overflow: hidden;
            transform: translateY(-10px);
            opacity: 0;
            transition: all 0.3s ease;
        `;
        
        // إضافة محتوى الإشعارات
        panel.innerHTML = `
            <div style="padding: 1rem; border-bottom: 1px solid var(--gray-200);">
                <h4 style="margin: 0; font-size: 1rem; font-weight: 600;">الإشعارات</h4>
            </div>
            <div style="max-height: 300px; overflow-y: auto;">
                <div style="padding: 1rem; text-align: center; color: var(--gray-500);">
                    <i class="fas fa-bell-slash" style="font-size: 2rem; margin-bottom: 0.5rem; display: block;"></i>
                    لا توجد إشعارات جديدة
                </div>
            </div>
            <div style="padding: 0.75rem; border-top: 1px solid var(--gray-200); text-align: center;">
                <a href="notifications.php" style="color: var(--primary-color); text-decoration: none; font-size: 0.875rem;">
                    عرض جميع الإشعارات
                </a>
            </div>
        `;
        
        // تحديد موضع اللوحة
        const rect = notificationBtn.getBoundingClientRect();
        panel.style.position = 'fixed';
        panel.style.top = rect.bottom + 10 + 'px';
        panel.style.left = rect.left - 300 + 'px';
        
        document.body.appendChild(panel);
        
        // تأثير الظهور
        setTimeout(() => {
            panel.style.transform = 'translateY(0)';
            panel.style.opacity = '1';
        }, 10);
        
        // إغلاق اللوحة عند النقر خارجها
        setTimeout(() => {
            document.addEventListener('click', function closePanel(e) {
                if (!panel.contains(e.target) && !notificationBtn.contains(e.target)) {
                    panel.style.transform = 'translateY(-10px)';
                    panel.style.opacity = '0';
                    setTimeout(() => panel.remove(), 300);
                    document.removeEventListener('click', closePanel);
                }
            });
        }, 100);
        
        // تحميل الإشعارات الفعلية
        loadNotifications(panel);
    }

    // تحميل الإشعارات
    function loadNotifications(panel) {
        fetch('api/get_notifications.php')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.notifications.length > 0) {
                    const container = panel.querySelector('div:nth-child(2)');
                    container.innerHTML = '';
                    
                    data.notifications.forEach(notification => {
                        const item = document.createElement('div');
                        item.style.cssText = `
                            padding: 1rem;
                            border-bottom: 1px solid var(--gray-100);
                            cursor: pointer;
                            transition: background-color 0.2s ease;
                        `;
                        
                        item.innerHTML = `
                            <div style="font-weight: 500; margin-bottom: 0.25rem; font-size: 0.875rem;">
                                ${notification.title}
                            </div>
                            <div style="color: var(--gray-600); font-size: 0.75rem; margin-bottom: 0.25rem;">
                                ${notification.message}
                            </div>
                            <div style="color: var(--gray-500); font-size: 0.75rem;">
                                ${notification.created_at}
                            </div>
                        `;
                        
                        item.addEventListener('mouseenter', function() {
                            this.style.backgroundColor = 'var(--gray-50)';
                        });
                        
                        item.addEventListener('mouseleave', function() {
                            this.style.backgroundColor = 'transparent';
                        });
                        
                        container.appendChild(item);
                    });
                }
            })
            .catch(error => {
                console.error('Error loading notifications:', error);
            });
    }

    // تأثيرات التمرير السلس
    const scrollLinks = document.querySelectorAll('a[href^="#"]');
    scrollLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // تحسين الأداء - lazy loading للرسوم البيانية
    const chartContainers = document.querySelectorAll('canvas');
    const chartObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // تحميل الرسم البياني عند ظهوره في الشاشة
                entry.target.classList.add('chart-visible');
                chartObserver.unobserve(entry.target);
            }
        });
    });

    chartContainers.forEach(chart => {
        chartObserver.observe(chart);
    });

    // حفظ حالة الشريط الجانبي
    const sidebarState = localStorage.getItem('sidebarCollapsed');
    if (sidebarState === 'true' && window.innerWidth > 768) {
        sidebar.classList.add('collapsed');
        mainContent.classList.add('expanded');
    }

    // إضافة اختصارات لوحة المفاتيح
    document.addEventListener('keydown', function(e) {
        // Ctrl + / لتبديل الشريط الجانبي
        if (e.ctrlKey && e.key === '/') {
            e.preventDefault();
            if (sidebarToggle) {
                sidebarToggle.click();
            }
        }
        
        // Escape لإغلاق اللوحات المفتوحة
        if (e.key === 'Escape') {
            const panels = document.querySelectorAll('.notification-panel');
            panels.forEach(panel => panel.remove());
            
            sidebar.classList.remove('active');
            removeOverlay();
        }
    });

    console.log('Dashboard initialized successfully');
});
