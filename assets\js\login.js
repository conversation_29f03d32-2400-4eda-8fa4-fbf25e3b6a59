/**
 * JavaScript لصفحة تسجيل الدخول
 * Modern Login Page JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // عناصر النموذج
    const loginForm = document.getElementById('loginForm');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const passwordToggle = document.getElementById('passwordToggle');
    const loginBtn = document.getElementById('loginBtn');
    const loadingSpinner = document.querySelector('.loading');
    const btnText = document.querySelector('.btn-text');

    // إضافة تأثير الظهور التدريجي
    document.querySelector('.login-card').classList.add('fade-in');

    // تبديل إظهار/إخفاء كلمة المرور
    if (passwordToggle) {
        passwordToggle.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            const icon = this.querySelector('i');
            if (type === 'text') {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    }

    // التحقق من صحة البيانات في الوقت الفعلي
    function validateInput(input) {
        const value = input.value.trim();
        const isValid = value.length > 0;
        
        if (isValid) {
            input.classList.remove('error');
            input.classList.add('valid');
        } else {
            input.classList.remove('valid');
            input.classList.add('error');
        }
        
        return isValid;
    }

    // إضافة مستمعي الأحداث للتحقق
    usernameInput.addEventListener('blur', function() {
        validateInput(this);
    });

    passwordInput.addEventListener('blur', function() {
        validateInput(this);
    });

    // إزالة رسائل الخطأ عند الكتابة
    usernameInput.addEventListener('input', function() {
        this.classList.remove('error');
        clearErrorMessages();
    });

    passwordInput.addEventListener('input', function() {
        this.classList.remove('error');
        clearErrorMessages();
    });

    // مسح رسائل الخطأ
    function clearErrorMessages() {
        const alerts = document.querySelectorAll('.alert-error');
        alerts.forEach(alert => {
            alert.style.opacity = '0';
            setTimeout(() => alert.remove(), 300);
        });
    }

    // عرض رسالة خطأ
    function showError(message) {
        clearErrorMessages();
        
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-error';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-circle"></i>
            <span>${message}</span>
        `;
        
        const form = document.querySelector('.login-form');
        form.insertBefore(alertDiv, form.firstChild);
        
        // تأثير الظهور
        setTimeout(() => {
            alertDiv.style.opacity = '1';
        }, 10);
    }

    // عرض رسالة نجاح
    function showSuccess(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success';
        alertDiv.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>${message}</span>
        `;
        
        const form = document.querySelector('.login-form');
        form.insertBefore(alertDiv, form.firstChild);
    }

    // تبديل حالة التحميل
    function toggleLoading(isLoading) {
        if (isLoading) {
            loginBtn.disabled = true;
            btnText.style.display = 'none';
            loadingSpinner.style.display = 'flex';
            loginBtn.style.opacity = '0.8';
        } else {
            loginBtn.disabled = false;
            btnText.style.display = 'block';
            loadingSpinner.style.display = 'none';
            loginBtn.style.opacity = '1';
        }
    }

    // معالجة إرسال النموذج
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // التحقق من صحة البيانات
            const isUsernameValid = validateInput(usernameInput);
            const isPasswordValid = validateInput(passwordInput);
            
            if (!isUsernameValid || !isPasswordValid) {
                showError('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            // بدء التحميل
            toggleLoading(true);

            // إرسال البيانات
            const formData = new FormData(this);
            
            fetch('auth/login_process.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                toggleLoading(false);
                
                if (data.success) {
                    showSuccess('تم تسجيل الدخول بنجاح! جاري التحويل...');
                    
                    // تأخير قصير قبل التحويل
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1500);
                } else {
                    showError(data.message || 'حدث خطأ أثناء تسجيل الدخول');
                }
            })
            .catch(error => {
                toggleLoading(false);
                console.error('Error:', error);
                showError('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى');
            });
        });
    }

    // تأثيرات إضافية للحقول
    const formInputs = document.querySelectorAll('.form-input');
    formInputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            if (this.value === '') {
                this.parentElement.classList.remove('focused');
            }
        });
        
        // إذا كان الحقل يحتوي على قيمة عند التحميل
        if (input.value !== '') {
            input.parentElement.classList.add('focused');
        }
    });

    // تأثير الضغط على زر الدخول
    loginBtn.addEventListener('mousedown', function() {
        this.style.transform = 'translateY(1px)';
    });

    loginBtn.addEventListener('mouseup', function() {
        this.style.transform = 'translateY(-2px)';
    });

    // اختصارات لوحة المفاتيح
    document.addEventListener('keydown', function(e) {
        // Enter للإرسال
        if (e.key === 'Enter' && !loginBtn.disabled) {
            loginForm.dispatchEvent(new Event('submit'));
        }
        
        // Escape لمسح النموذج
        if (e.key === 'Escape') {
            usernameInput.value = '';
            passwordInput.value = '';
            clearErrorMessages();
        }
    });

    // حفظ اسم المستخدم في التخزين المحلي
    const rememberCheckbox = document.getElementById('remember');
    
    // استرجاع اسم المستخدم المحفوظ
    const savedUsername = localStorage.getItem('rememberedUsername');
    if (savedUsername && rememberCheckbox) {
        usernameInput.value = savedUsername;
        rememberCheckbox.checked = true;
        usernameInput.parentElement.classList.add('focused');
    }

    // حفظ اسم المستخدم عند النجاح
    if (rememberCheckbox) {
        loginForm.addEventListener('submit', function() {
            if (rememberCheckbox.checked) {
                localStorage.setItem('rememberedUsername', usernameInput.value);
            } else {
                localStorage.removeItem('rememberedUsername');
            }
        });
    }

    // تأثير الجسيمات في الخلفية (اختياري)
    function createParticle() {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255,255,255,0.5);
            border-radius: 50%;
            pointer-events: none;
            animation: float 3s linear infinite;
        `;
        
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 3 + 's';
        
        document.body.appendChild(particle);
        
        setTimeout(() => {
            particle.remove();
        }, 3000);
    }

    // إنشاء جسيمات بشكل دوري
    setInterval(createParticle, 500);
});

// إضافة CSS للتأثيرات الإضافية
const additionalStyles = `
    .form-input.error {
        border-color: var(--error-color);
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }
    
    .form-input.valid {
        border-color: var(--success-color);
        box-shadow: 0 0 0 3px rgba(74, 222, 128, 0.1);
    }
    
    .form-group.focused .input-icon {
        color: var(--primary-color);
        transform: translateY(-50%) scale(1.1);
    }
    
    @keyframes float {
        0% {
            transform: translateY(100vh) rotate(0deg);
            opacity: 0;
        }
        10% {
            opacity: 1;
        }
        90% {
            opacity: 1;
        }
        100% {
            transform: translateY(-100px) rotate(360deg);
            opacity: 0;
        }
    }
`;

// إضافة الأنماط إلى الصفحة
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);
