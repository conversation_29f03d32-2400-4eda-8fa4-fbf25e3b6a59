<?php
/**
 * معالجة تسجيل الدخول
 * Login Process Handler
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

// تعيين نوع المحتوى
header('Content-Type: application/json; charset=utf-8');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'طريقة طلب غير مسموحة'
    ]);
    exit;
}

// التحقق من CSRF Token
if (!isset($_POST['csrf_token']) || !isset($_SESSION['csrf_token']) || 
    !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
    echo json_encode([
        'success' => false,
        'message' => 'رمز الأمان غير صحيح'
    ]);
    exit;
}

// تنظيف البيانات المدخلة
$username = sanitizeInput($_POST['username'] ?? '');
$password = $_POST['password'] ?? '';
$remember = isset($_POST['remember']) && $_POST['remember'] === '1';

// التحقق من وجود البيانات
if (empty($username) || empty($password)) {
    echo json_encode([
        'success' => false,
        'message' => 'يرجى ملء جميع الحقول المطلوبة'
    ]);
    exit;
}

try {
    // الاتصال بقاعدة البيانات
    $database = new Database();
    $conn = $database->getConnection();
    
    // البحث عن المستخدم
    $query = "SELECT id, username, email, password, full_name, role, is_active, last_login 
              FROM users 
              WHERE (username = ? OR email = ?) AND is_active = 1 
              LIMIT 1";
    
    $stmt = $conn->prepare($query);
    $stmt->execute([$username, $username]);
    $user = $stmt->fetch();
    
    // التحقق من وجود المستخدم وصحة كلمة المرور
    if (!$user || !verifyPassword($password, $user['password'])) {
        // تسجيل محاولة دخول فاشلة
        logFailedLogin($username, $_SERVER['REMOTE_ADDR']);
        
        echo json_encode([
            'success' => false,
            'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'
        ]);
        exit;
    }
    
    // التحقق من حالة الحساب
    if (!$user['is_active']) {
        echo json_encode([
            'success' => false,
            'message' => 'حسابك غير مفعل. يرجى التواصل مع مدير النظام'
        ]);
        exit;
    }
    
    // تحديث آخر تسجيل دخول
    $updateQuery = "UPDATE users SET last_login = NOW() WHERE id = ?";
    $updateStmt = $conn->prepare($updateQuery);
    $updateStmt->execute([$user['id']]);
    
    // إنشاء الجلسة
    session_regenerate_id(true);
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['full_name'] = $user['full_name'];
    $_SESSION['email'] = $user['email'];
    $_SESSION['role'] = $user['role'];
    $_SESSION['last_activity'] = time();
    $_SESSION['login_time'] = time();
    
    // تعيين كوكيز التذكر إذا طُلب ذلك
    if ($remember) {
        $token = bin2hex(random_bytes(32));
        $hashedToken = hash('sha256', $token);
        $expires = time() + (30 * 24 * 60 * 60); // 30 يوم
        
        // حفظ الرمز في قاعدة البيانات
        $tokenQuery = "INSERT INTO remember_tokens (user_id, token_hash, expires_at) VALUES (?, ?, ?)";
        $tokenStmt = $conn->prepare($tokenQuery);
        $tokenStmt->execute([$user['id'], $hashedToken, date('Y-m-d H:i:s', $expires)]);
        
        // تعيين الكوكيز
        setcookie('remember_token', $token, $expires, '/', '', false, true);
    }
    
    // تسجيل نشاط تسجيل الدخول
    logActivity($user['id'], 'login', 'تسجيل دخول ناجح من IP: ' . $_SERVER['REMOTE_ADDR']);
    
    // تحديد صفحة التوجيه حسب الدور
    $redirectUrl = '';
    switch ($user['role']) {
        case 'admin':
            $redirectUrl = '../admin/dashboard.php';
            break;
        case 'supervisor':
            $redirectUrl = '../supervisor/dashboard.php';
            break;
        case 'user':
            $redirectUrl = '../user/dashboard.php';
            break;
        default:
            $redirectUrl = '../index.php';
    }
    
    // إرسال استجابة النجاح
    echo json_encode([
        'success' => true,
        'message' => 'تم تسجيل الدخول بنجاح',
        'redirect' => $redirectUrl,
        'user' => [
            'id' => $user['id'],
            'name' => $user['full_name'],
            'role' => $user['role']
        ]
    ]);
    
} catch (PDOException $e) {
    // تسجيل الخطأ
    error_log("Login Error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى'
    ]);
} catch (Exception $e) {
    // تسجيل الخطأ
    error_log("Login Error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى'
    ]);
}

/**
 * تسجيل محاولات الدخول الفاشلة
 */
function logFailedLogin($username, $ip) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $query = "INSERT INTO failed_logins (username, ip_address, attempted_at) VALUES (?, ?, NOW())";
        $stmt = $conn->prepare($query);
        $stmt->execute([$username, $ip]);
        
        // التحقق من عدد المحاولات الفاشلة
        $checkQuery = "SELECT COUNT(*) as attempts FROM failed_logins 
                       WHERE ip_address = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL 15 MINUTE)";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->execute([$ip]);
        $result = $checkStmt->fetch();
        
        // إذا تجاوز عدد المحاولات الحد المسموح
        if ($result['attempts'] > 5) {
            // يمكن إضافة منطق حظر IP هنا
            error_log("Too many failed login attempts from IP: $ip");
        }
        
    } catch (Exception $e) {
        error_log("Failed to log failed login: " . $e->getMessage());
    }
}
?>
