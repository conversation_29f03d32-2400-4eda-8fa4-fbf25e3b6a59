<?php
/**
 * تسجيل الخروج
 * Logout Handler
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../index.php');
}

try {
    // تسجيل نشاط تسجيل الخروج
    if (isset($_SESSION['user_id'])) {
        logActivity($_SESSION['user_id'], 'logout', 'تسجيل خروج من IP: ' . $_SERVER['REMOTE_ADDR']);
    }
    
    // حذف رمز التذكر إذا كان موجوداً
    if (isset($_COOKIE['remember_token'])) {
        $database = new Database();
        $conn = $database->getConnection();
        
        $token = $_COOKIE['remember_token'];
        $hashedToken = hash('sha256', $token);
        
        // حذف الرمز من قاعدة البيانات
        $query = "DELETE FROM remember_tokens WHERE token_hash = ?";
        $stmt = $conn->prepare($query);
        $stmt->execute([$hashedToken]);
        
        // حذف الكوكيز
        setcookie('remember_token', '', time() - 3600, '/', '', false, true);
    }
    
    // تدمير الجلسة
    session_unset();
    session_destroy();
    
    // إنشاء جلسة جديدة لرسالة النجاح
    session_start();
    showMessage('تم تسجيل الخروج بنجاح', 'success');
    
} catch (Exception $e) {
    // تسجيل الخطأ
    error_log("Logout Error: " . $e->getMessage());
    
    // تدمير الجلسة حتى لو حدث خطأ
    session_unset();
    session_destroy();
    session_start();
    showMessage('تم تسجيل الخروج', 'info');
}

// إعادة التوجيه إلى صفحة تسجيل الدخول
redirect('../index.php');
?>
