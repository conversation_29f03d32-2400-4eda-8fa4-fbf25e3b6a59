<?php
/**
 * ملف فحص الأخطاء
 * Debug File
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<h1>فحص النظام - System Debug</h1>";
echo "<hr>";

// فحص إصدار PHP
echo "<h2>معلومات PHP - PHP Information</h2>";
echo "إصدار PHP: " . PHP_VERSION . "<br>";
echo "نظام التشغيل: " . PHP_OS . "<br>";
echo "<br>";

// فحص الملفات المطلوبة
echo "<h2>فحص الملفات - File Check</h2>";

$required_files = [
    'config/database.php',
    'includes/functions.php',
    'assets/css/variables.css',
    'assets/css/login.css',
    'assets/js/login.js'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "✅ $file - موجود<br>";
    } else {
        echo "❌ $file - مفقود<br>";
    }
}
echo "<br>";

// فحص الإعدادات
echo "<h2>فحص الإعدادات - Configuration Check</h2>";

try {
    if (file_exists('config/database.php')) {
        require_once 'config/database.php';
        echo "✅ تم تحميل ملف الإعدادات بنجاح<br>";
        
        // عرض الثوابت المعرفة
        if (defined('SITE_NAME')) {
            echo "✅ SITE_NAME: " . SITE_NAME . "<br>";
        } else {
            echo "❌ SITE_NAME غير معرف<br>";
        }
        
        if (defined('SITE_URL')) {
            echo "✅ SITE_URL: " . SITE_URL . "<br>";
        } else {
            echo "❌ SITE_URL غير معرف<br>";
        }
        
    } else {
        echo "❌ ملف الإعدادات مفقود<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في تحميل الإعدادات: " . $e->getMessage() . "<br>";
}
echo "<br>";

// فحص قاعدة البيانات
echo "<h2>فحص قاعدة البيانات - Database Check</h2>";

try {
    if (class_exists('Database')) {
        $database = new Database();
        $conn = $database->getConnection();
        
        if ($conn) {
            echo "✅ الاتصال بقاعدة البيانات ناجح<br>";
            
            // فحص الجداول
            $tables = ['users', 'tasks', 'notifications'];
            foreach ($tables as $table) {
                try {
                    $stmt = $conn->query("SELECT COUNT(*) FROM $table");
                    $count = $stmt->fetchColumn();
                    echo "✅ جدول $table: $count سجل<br>";
                } catch (PDOException $e) {
                    echo "❌ جدول $table: غير موجود أو خطأ<br>";
                }
            }
        } else {
            echo "❌ فشل الاتصال بقاعدة البيانات<br>";
        }
    } else {
        echo "❌ فئة Database غير موجودة<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
}
echo "<br>";

// فحص الدوال
echo "<h2>فحص الدوال - Functions Check</h2>";

try {
    if (file_exists('includes/functions.php')) {
        require_once 'includes/functions.php';
        echo "✅ تم تحميل ملف الدوال بنجاح<br>";
        
        // فحص الدوال المهمة
        $functions = ['isLoggedIn', 'hasRole', 'sanitizeInput', 'redirect'];
        foreach ($functions as $func) {
            if (function_exists($func)) {
                echo "✅ دالة $func: موجودة<br>";
            } else {
                echo "❌ دالة $func: مفقودة<br>";
            }
        }
    } else {
        echo "❌ ملف الدوال مفقود<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في تحميل الدوال: " . $e->getMessage() . "<br>";
}
echo "<br>";

// فحص الجلسة
echo "<h2>فحص الجلسة - Session Check</h2>";

if (session_status() === PHP_SESSION_ACTIVE) {
    echo "✅ الجلسة نشطة<br>";
    echo "معرف الجلسة: " . session_id() . "<br>";
} else {
    echo "❌ الجلسة غير نشطة<br>";
    try {
        session_start();
        echo "✅ تم بدء الجلسة<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في بدء الجلسة: " . $e->getMessage() . "<br>";
    }
}
echo "<br>";

// فحص الصلاحيات
echo "<h2>فحص الصلاحيات - Permissions Check</h2>";

$directories = ['uploads', 'config', 'includes', 'assets'];
foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_readable($dir)) {
            echo "✅ مجلد $dir: قابل للقراءة<br>";
        } else {
            echo "❌ مجلد $dir: غير قابل للقراءة<br>";
        }
        
        if (is_writable($dir)) {
            echo "✅ مجلد $dir: قابل للكتابة<br>";
        } else {
            echo "⚠️ مجلد $dir: غير قابل للكتابة<br>";
        }
    } else {
        echo "❌ مجلد $dir: غير موجود<br>";
    }
}
echo "<br>";

// فحص الإضافات المطلوبة
echo "<h2>فحص إضافات PHP - PHP Extensions Check</h2>";

$extensions = ['pdo', 'pdo_mysql', 'json', 'session', 'mbstring'];
foreach ($extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ إضافة $ext: مثبتة<br>";
    } else {
        echo "❌ إضافة $ext: غير مثبتة<br>";
    }
}
echo "<br>";

// معلومات الخادم
echo "<h2>معلومات الخادم - Server Information</h2>";
echo "خادم الويب: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "المنفذ: " . $_SERVER['SERVER_PORT'] . "<br>";
echo "اسم الخادم: " . $_SERVER['SERVER_NAME'] . "<br>";
echo "مسار الوثيقة: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "المسار الحالي: " . __DIR__ . "<br>";
echo "<br>";

echo "<hr>";
echo "<h3>انتهى الفحص - Debug Complete</h3>";
echo "<p><a href='index.php'>العودة للصفحة الرئيسية</a></p>";
echo "<p><a href='setup.php'>إعداد النظام</a></p>";
?>
