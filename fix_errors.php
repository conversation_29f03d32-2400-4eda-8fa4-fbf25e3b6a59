<?php
/**
 * إصلاح أخطاء النظام
 * Fix System Errors
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 إصلاح أخطاء النظام</h1>";
echo "<hr>";

// 1. إنشاء المجلدات المطلوبة
echo "<h2>📁 إنشاء المجلدات</h2>";

$directories = ['uploads', 'logs', 'temp'];
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "✅ تم إنشاء مجلد: $dir<br>";
        } else {
            echo "❌ فشل إنشاء مجلد: $dir<br>";
        }
    } else {
        echo "✅ مجلد موجود: $dir<br>";
    }
}

// 2. إنشاء ملف .htaccess للحماية
echo "<h2>🛡️ إنشاء ملفات الحماية</h2>";

$htaccess_uploads = "uploads/.htaccess";
if (!file_exists($htaccess_uploads)) {
    $content = "# حماية مجلد الرفع\nOptions -Indexes\n<Files \"*.php\">\nOrder Deny,Allow\nDeny from all\n</Files>";
    if (file_put_contents($htaccess_uploads, $content)) {
        echo "✅ تم إنشاء ملف حماية uploads<br>";
    } else {
        echo "❌ فشل إنشاء ملف حماية uploads<br>";
    }
} else {
    echo "✅ ملف حماية uploads موجود<br>";
}

// 3. فحص وإصلاح ملفات PHP
echo "<h2>🔍 فحص ملفات PHP</h2>";

$php_files = [
    'index.php',
    'config/database.php',
    'includes/functions.php'
];

foreach ($php_files as $file) {
    if (file_exists($file)) {
        // فحص صحة PHP
        $output = [];
        $return_var = 0;
        exec("php -l \"$file\" 2>&1", $output, $return_var);
        
        if ($return_var === 0) {
            echo "✅ $file: صحيح<br>";
        } else {
            echo "❌ $file: يحتوي على أخطاء<br>";
            echo "<pre style='color: red; font-size: 12px;'>" . implode("\n", $output) . "</pre>";
        }
    } else {
        echo "❌ $file: مفقود<br>";
    }
}

// 4. إنشاء ملف إعدادات مبسط
echo "<h2>⚙️ إنشاء ملف إعدادات مبسط</h2>";

$simple_config = "config/simple_config.php";
$config_content = '<?php
// إعدادات مبسطة للنظام
define("SITE_NAME", "نظام إدارة المهام");
define("SITE_URL", "http://localhost:8080");
define("DB_HOST", "localhost");
define("DB_NAME", "task_management_system");
define("DB_USER", "root");
define("DB_PASS", "");

// دالة اتصال بسيطة بقاعدة البيانات
function getSimpleConnection() {
    try {
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        return null;
    }
}

// دوال مساعدة بسيطة
function isLoggedIn() {
    return isset($_SESSION["user_id"]);
}

function redirect($url) {
    header("Location: " . $url);
    exit;
}
?>';

if (file_put_contents($simple_config, $config_content)) {
    echo "✅ تم إنشاء ملف الإعدادات المبسط<br>";
} else {
    echo "❌ فشل إنشاء ملف الإعدادات المبسط<br>";
}

// 5. إنشاء صفحة index مبسطة وآمنة
echo "<h2>📄 إنشاء صفحة index آمنة</h2>";

$safe_index = 'index_safe.php';
$index_content = '<?php
session_start();
require_once "config/simple_config.php";

$message = "";
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $username = $_POST["username"] ?? "";
    $password = $_POST["password"] ?? "";
    
    // تحقق بسيط
    if ($username === "admin" && $password === "password123") {
        $_SESSION["user_id"] = 1;
        $_SESSION["username"] = "admin";
        $_SESSION["role"] = "admin";
        $_SESSION["full_name"] = "مدير النظام";
        redirect("admin_dashboard.php");
    } else {
        $message = "اسم المستخدم أو كلمة المرور غير صحيحة";
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تسجيل الدخول - <?php echo SITE_NAME; ?></title>
    <style>
        body { font-family: Arial, sans-serif; direction: rtl; background: #f5f5f5; margin: 0; padding: 40px; }
        .container { max-width: 400px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { text-align: center; color: #333; margin-bottom: 30px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; }
        button { width: 100%; padding: 12px; background: #667eea; color: white; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; }
        button:hover { background: #5a6fd8; }
        .error { color: red; text-align: center; margin-bottom: 20px; }
        .demo { background: #f0f0f0; padding: 15px; border-radius: 5px; margin-top: 20px; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 تسجيل الدخول</h1>
        <?php if ($message): ?>
            <div class="error"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        <form method="POST">
            <div class="form-group">
                <label>اسم المستخدم:</label>
                <input type="text" name="username" required>
            </div>
            <div class="form-group">
                <label>كلمة المرور:</label>
                <input type="password" name="password" required>
            </div>
            <button type="submit">دخول</button>
        </form>
        <div class="demo">
            <strong>حسابات تجريبية:</strong><br>
            المدير: admin / password123
        </div>
    </div>
</body>
</html>';

if (file_put_contents($safe_index, $index_content)) {
    echo "✅ تم إنشاء صفحة index آمنة<br>";
} else {
    echo "❌ فشل إنشاء صفحة index آمنة<br>";
}

// 6. فحص قاعدة البيانات
echo "<h2>🗄️ فحص قاعدة البيانات</h2>";

try {
    $pdo = new PDO("mysql:host=localhost;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // فحص وجود قاعدة البيانات
    $stmt = $pdo->query("SHOW DATABASES LIKE 'task_management_system'");
    if ($stmt->rowCount() > 0) {
        echo "✅ قاعدة البيانات موجودة<br>";
    } else {
        echo "⚠️ قاعدة البيانات غير موجودة - سيتم إنشاؤها<br>";
        $pdo->exec("CREATE DATABASE task_management_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "✅ تم إنشاء قاعدة البيانات<br>";
    }
    
} catch (PDOException $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h2>🎉 انتهى الإصلاح</h2>";
echo "<p><strong>الصفحات المتاحة:</strong></p>";
echo "<ul>";
echo "<li><a href='index_safe.php'>صفحة تسجيل دخول آمنة</a></li>";
echo "<li><a href='index_simple.php'>صفحة تسجيل دخول مبسطة</a></li>";
echo "<li><a href='test.php'>اختبار النظام</a></li>";
echo "<li><a href='debug.php'>فحص الأخطاء</a></li>";
echo "<li><a href='setup.php'>إعداد النظام</a></li>";
echo "</ul>";

echo "<p><strong>ملاحظة:</strong> إذا كانت الصفحة الأصلية index.php لا تعمل، استخدم index_safe.php</p>";
?>
