<?php
/**
 * الدوال المساعدة للنظام
 * Helper Functions
 */

require_once 'config/database.php';

/**
 * تشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * التحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * تنظيف البيانات المدخلة
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * التحقق من تسجيل الدخول
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * التحقق من الدور
 */
function hasRole($role) {
    return isset($_SESSION['role']) && $_SESSION['role'] === $role;
}

/**
 * التحقق من الصلاحية
 */
function hasPermission($permission) {
    if (!isLoggedIn()) return false;
    
    $role = $_SESSION['role'];
    
    switch ($permission) {
        case 'create_task':
            return in_array($role, ['admin', 'supervisor']);
        case 'view_all_tasks':
            return in_array($role, ['admin', 'supervisor']);
        case 'manage_users':
            return $role === 'admin';
        case 'view_reports':
            return in_array($role, ['admin', 'supervisor']);
        default:
            return false;
    }
}

/**
 * إعادة التوجيه
 */
function redirect($url) {
    header("Location: " . $url);
    exit();
}

/**
 * عرض الرسائل
 */
function showMessage($message, $type = 'info') {
    $_SESSION['message'] = $message;
    $_SESSION['message_type'] = $type;
}

/**
 * الحصول على الرسائل وحذفها
 */
function getAndClearMessage() {
    if (isset($_SESSION['message'])) {
        $message = $_SESSION['message'];
        $type = $_SESSION['message_type'] ?? 'info';
        unset($_SESSION['message'], $_SESSION['message_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}

/**
 * تنسيق التاريخ
 */
function formatDate($date, $format = 'Y-m-d H:i') {
    return date($format, strtotime($date));
}

/**
 * تنسيق التاريخ بالعربية
 */
function formatDateArabic($date) {
    $months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $timestamp = strtotime($date);
    $day = date('d', $timestamp);
    $month = $months[(int)date('m', $timestamp)];
    $year = date('Y', $timestamp);
    
    return "$day $month $year";
}

/**
 * حساب الأيام المتبقية
 */
function getDaysRemaining($dueDate) {
    $today = new DateTime();
    $due = new DateTime($dueDate);
    $diff = $today->diff($due);
    
    if ($due < $today) {
        return -$diff->days; // متأخر
    }
    return $diff->days;
}

/**
 * الحصول على لون حالة المهمة
 */
function getTaskStatusColor($status) {
    switch ($status) {
        case 'pending': return 'warning';
        case 'in_progress': return 'info';
        case 'completed': return 'success';
        case 'overdue': return 'danger';
        default: return 'secondary';
    }
}

/**
 * الحصول على لون الأولوية
 */
function getPriorityColor($priority) {
    switch ($priority) {
        case 'low': return 'success';
        case 'medium': return 'warning';
        case 'high': return 'danger';
        case 'urgent': return 'dark';
        default: return 'secondary';
    }
}

/**
 * إنشاء إشعار
 */
function createNotification($userId, $taskId, $type, $title, $message) {
    $db = new Database();
    $conn = $db->getConnection();
    
    $query = "INSERT INTO notifications (user_id, task_id, type, title, message) VALUES (?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($query);
    return $stmt->execute([$userId, $taskId, $type, $title, $message]);
}

/**
 * رفع الملفات
 */
function uploadFile($file, $allowedTypes = ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']) {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return false;
    }
    
    $uploadDir = 'uploads/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }
    
    $fileName = time() . '_' . basename($file['name']);
    $targetPath = $uploadDir . $fileName;
    $fileType = strtolower(pathinfo($targetPath, PATHINFO_EXTENSION));
    
    if (!in_array($fileType, $allowedTypes)) {
        return false;
    }
    
    if (move_uploaded_file($file['tmp_name'], $targetPath)) {
        return $fileName;
    }
    
    return false;
}

/**
 * تسجيل العمليات
 */
function logActivity($userId, $action, $details = '') {
    $db = new Database();
    $conn = $db->getConnection();
    
    $query = "INSERT INTO activity_logs (user_id, action, details, ip_address, created_at) VALUES (?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($query);
    $stmt->execute([$userId, $action, $details, $_SERVER['REMOTE_ADDR']]);
}
?>
