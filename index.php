<?php
/**
 * صفحة تسجيل الدخول الرئيسية
 * Main Login Page
 */

// بدء الجلسة أولاً
session_start();

// تفعيل عرض الأخطاء للتطوير
error_reporting(E_ALL);
ini_set('display_errors', 1);

// تحميل الملفات المطلوبة
require_once 'config/database.php';
require_once 'includes/functions.php';

// إعادة توجيه المستخدم المسجل دخوله
if (isLoggedIn()) {
    $role = $_SESSION['role'];
    switch ($role) {
        case 'admin':
            redirect('admin/dashboard.php');
            break;
        case 'supervisor':
            redirect('supervisor/dashboard.php');
            break;
        case 'user':
            redirect('user/dashboard.php');
            break;
        default:
            redirect('index.php');
    }
}

// الحصول على الرسائل
$message = getAndClearMessage();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - <?php echo SITE_NAME; ?></title>

    <!-- Meta Tags -->
    <meta name="description" content="نظام إدارة المهام المتطور للشركات - تسجيل الدخول">
    <meta name="keywords" content="إدارة المهام, نظام إدارة, مهام الشركة, تسجيل دخول">
    <meta name="author" content="Task Management System">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">

    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/variables.css">
    <link rel="stylesheet" href="assets/css/login.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

    <!-- Preload Critical Resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" as="style">
    <link rel="preload" href="assets/js/login.js" as="script">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <!-- Header -->
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-tasks"></i>
                </div>
                <h1 class="login-title">مرحباً بك</h1>
                <p class="login-subtitle">سجل دخولك لإدارة مهامك بكفاءة</p>
            </div>

            <!-- Login Form -->
            <form id="loginForm" class="login-form" method="POST" action="auth/login_process.php">
                <!-- عرض الرسائل -->
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message['type'] === 'error' ? 'error' : 'success'; ?>">
                        <i class="fas fa-<?php echo $message['type'] === 'error' ? 'exclamation-circle' : 'check-circle'; ?>"></i>
                        <span><?php echo htmlspecialchars($message['message']); ?></span>
                    </div>
                <?php endif; ?>

                <!-- Username Field -->
                <div class="form-group">
                    <label for="username" class="form-label">اسم المستخدم أو البريد الإلكتروني</label>
                    <input
                        type="text"
                        id="username"
                        name="username"
                        class="form-input"
                        placeholder="أدخل اسم المستخدم أو البريد الإلكتروني"
                        required
                        autocomplete="username"
                        autofocus
                    >
                    <i class="fas fa-user input-icon"></i>
                </div>

                <!-- Password Field -->
                <div class="form-group">
                    <label for="password" class="form-label">كلمة المرور</label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        class="form-input"
                        placeholder="أدخل كلمة المرور"
                        required
                        autocomplete="current-password"
                    >
                    <i class="fas fa-lock input-icon"></i>
                    <button type="button" id="passwordToggle" class="password-toggle" aria-label="إظهار/إخفاء كلمة المرور">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>

                <!-- Form Options -->
                <div class="form-options">
                    <label class="remember-me">
                        <input type="checkbox" id="remember" name="remember" value="1">
                        <span>تذكرني</span>
                    </label>
                    <a href="auth/forgot_password.php" class="forgot-password">نسيت كلمة المرور؟</a>
                </div>

                <!-- Submit Button -->
                <button type="submit" id="loginBtn" class="login-btn">
                    <span class="btn-text">تسجيل الدخول</span>
                    <div class="loading">
                        <div class="spinner"></div>
                        <span>جاري التحقق...</span>
                    </div>
                </button>

                <!-- CSRF Token -->
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token'] = bin2hex(random_bytes(32)); ?>">
            </form>

            <!-- Additional Info -->
            <div class="login-footer" style="margin-top: 2rem; text-align: center; color: var(--gray-500); font-size: 0.875rem;">
                <p>للحصول على حساب جديد، تواصل مع مدير النظام</p>
                <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid var(--gray-200);">
                    <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </div>
    </div>



    <!-- JavaScript Files -->
    <script src="assets/js/login.js"></script>

    <!-- Service Worker for PWA (اختياري) -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('sw.js')
                    .then(function(registration) {
                        console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>

    <!-- Analytics (إضافة كود التتبع هنا إذا لزم الأمر) -->

</body>
</html>
