<?php
/**
 * صفحة تسجيل الدخول المبسطة
 * Simplified Login Page
 */

// بدء الجلسة
session_start();

// تفعيل عرض الأخطاء للتطوير
error_reporting(E_ALL);
ini_set('display_errors', 1);

// إعدادات أساسية
define('SITE_NAME', 'نظام إدارة المهام');
define('SITE_URL', 'http://localhost:8080');

// معالجة تسجيل الدخول
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    // تحقق بسيط (يمكن تحسينه لاحقاً)
    if ($username === 'admin' && $password === 'password123') {
        $_SESSION['user_id'] = 1;
        $_SESSION['username'] = 'admin';
        $_SESSION['role'] = 'admin';
        $_SESSION['full_name'] = 'مدير النظام';
        
        $message = 'تم تسجيل الدخول بنجاح!';
        $message_type = 'success';
        
        // إعادة توجيه بعد ثانيتين
        header("refresh:2;url=admin_dashboard.php");
    } else {
        $message = 'اسم المستخدم أو كلمة المرور غير صحيحة';
        $message_type = 'error';
    }
}

// التحقق من تسجيل الدخول
if (isset($_SESSION['user_id'])) {
    $message = 'أنت مسجل دخولك بالفعل';
    $message_type = 'info';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - <?php echo SITE_NAME; ?></title>
    
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a6fd8;
            --success-color: #10b981;
            --error-color: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-500: #6b7280;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --white: #ffffff;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            background: var(--gray-50);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray-800);
        }
        
        .login-container {
            width: 100%;
            max-width: 400px;
            margin: 0 20px;
        }
        
        .login-card {
            background: var(--white);
            border: 1px solid var(--gray-200);
            border-radius: 16px;
            padding: 2.5rem;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            position: relative;
        }
        
        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
            border-radius: 16px 16px 0 0;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .logo {
            width: 70px;
            height: 70px;
            margin: 0 auto 1rem;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--white);
        }
        
        .login-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 0.5rem;
        }
        
        .login-subtitle {
            color: var(--gray-500);
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }
        
        .form-input {
            width: 100%;
            padding: 0.875rem;
            border: 2px solid var(--gray-200);
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--white);
            color: var(--gray-800);
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .login-btn {
            width: 100%;
            padding: 0.875rem;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: var(--white);
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        
        .alert {
            padding: 0.875rem;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-right: 4px solid var(--success-color);
        }
        
        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
            border-right: 4px solid var(--error-color);
        }
        
        .alert-info {
            background: rgba(102, 126, 234, 0.1);
            color: var(--primary-color);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-right: 4px solid var(--primary-color);
        }
        
        .demo-info {
            margin-top: 1.5rem;
            padding: 1rem;
            background: var(--gray-100);
            border-radius: 12px;
            font-size: 0.8rem;
            color: var(--gray-700);
        }
        
        .demo-info h4 {
            margin-bottom: 0.5rem;
            color: var(--gray-800);
        }
        
        .footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid var(--gray-200);
            color: var(--gray-500);
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <!-- Header -->
            <div class="login-header">
                <div class="logo">📋</div>
                <h1 class="login-title">مرحباً بك</h1>
                <p class="login-subtitle">سجل دخولك لإدارة مهامك بكفاءة</p>
            </div>

            <!-- عرض الرسائل -->
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- Login Form -->
            <form method="POST">
                <div class="form-group">
                    <label for="username" class="form-label">اسم المستخدم</label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-input" 
                        placeholder="أدخل اسم المستخدم"
                        required
                        value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>"
                    >
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">كلمة المرور</label>
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        class="form-input" 
                        placeholder="أدخل كلمة المرور"
                        required
                    >
                </div>

                <button type="submit" class="login-btn">
                    تسجيل الدخول
                </button>
            </form>

            <!-- معلومات تجريبية -->
            <div class="demo-info">
                <h4>🔑 حسابات تجريبية:</h4>
                <p><strong>مدير:</strong> admin / password123</p>
                <p><strong>مشرف:</strong> supervisor1 / password123</p>
                <p><strong>موظف:</strong> user1 / password123</p>
            </div>

            <!-- Footer -->
            <div class="footer">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
                <p style="margin-top: 0.5rem;">
                    <a href="test.php" style="color: var(--primary-color);">اختبار النظام</a> | 
                    <a href="debug.php" style="color: var(--primary-color);">فحص الأخطاء</a> | 
                    <a href="setup.php" style="color: var(--primary-color);">إعداد النظام</a>
                </p>
            </div>
        </div>
    </div>
</body>
</html>
