@echo off
title نظام إدارة المهام - المنفذ 8080
echo ========================================
echo    نظام إدارة المهام - المنفذ 8080
echo    Task Management System - Port 8080
echo ========================================
echo.

REM التحقق من وجود PHP
where php >nul 2>&1
if %errorlevel% neq 0 (
    echo تحذير: PHP غير موجود في PATH
    echo Warning: PHP not found in PATH
    echo.
    echo جاري البحث عن PHP في XAMPP...
    echo Looking for PHP in XAMPP...
    
    if exist "C:\xampp\php\php.exe" (
        echo تم العثور على PHP في XAMPP
        echo Found PHP in XAMPP
        set PHP_PATH=C:\xampp\php\php.exe
    ) else (
        echo خطأ: لم يتم العثور على PHP
        echo Error: PHP not found
        echo.
        echo يرجى تثبيت PHP أو XAMPP
        echo Please install PHP or XAMPP
        pause
        exit /b 1
    )
) else (
    echo تم العثور على PHP
    echo PHP found
    set PHP_PATH=php
)

echo.
echo معلومات PHP:
echo PHP Information:
%PHP_PATH% --version
echo.

REM التحقق من المنفذ 8080
echo فحص المنفذ 8080...
echo Checking port 8080...
netstat -an | findstr :8080 >nul
if %errorlevel% equ 0 (
    echo تحذير: المنفذ 8080 مستخدم بالفعل
    echo Warning: Port 8080 is already in use
    echo.
    echo هل تريد المتابعة؟ (y/n)
    echo Do you want to continue? (y/n)
    set /p choice=
    if /i not "%choice%"=="y" (
        echo تم الإلغاء
        echo Cancelled
        pause
        exit /b 1
    )
) else (
    echo المنفذ 8080 متاح
    echo Port 8080 is available
)

echo.
echo بدء تشغيل الخادم...
echo Starting server...
echo.

echo ========================================
echo الخادم يعمل الآن على:
echo Server is now running on:
echo.
echo 🌐 http://localhost:8080/
echo.
echo للوصول للنظام:
echo To access the system:
echo 📝 تسجيل الدخول: http://localhost:8080/
echo 📝 Login: http://localhost:8080/
echo.
echo ⚙️  إعداد النظام: http://localhost:8080/setup.php
echo ⚙️  Setup: http://localhost:8080/setup.php
echo ========================================
echo.

echo 🔑 الحسابات التجريبية:
echo 🔑 Demo Accounts:
echo.
echo 👨‍💼 مدير / Admin: admin / password123
echo 👨‍💼 مشرف / Supervisor: supervisor1 / password123  
echo 👨‍💻 موظف / Employee: user1 / password123
echo.

echo لإيقاف الخادم اضغط Ctrl+C
echo To stop the server press Ctrl+C
echo.

REM فتح المتصفح تلقائياً بعد 3 ثوان
echo فتح المتصفح خلال 3 ثوان...
echo Opening browser in 3 seconds...
timeout /t 3 /nobreak >nul
start "" "http://localhost:8080/"

REM بدء خادم PHP
echo بدء خادم PHP...
echo Starting PHP server...
echo.
%PHP_PATH% -S localhost:8080 -t "%~dp0"

echo.
echo تم إيقاف الخادم
echo Server stopped
pause
