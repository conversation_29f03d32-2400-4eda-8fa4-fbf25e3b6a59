<?php
/**
 * ملف تهيئة النظام
 * System Setup File
 */

// التحقق من وجود ملف الإعدادات
if (file_exists('config/database.php')) {
    require_once 'config/database.php';
}

$setupComplete = false;
$errors = [];
$success = [];

// معالجة طلب التهيئة
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $dbHost = $_POST['db_host'] ?? 'localhost';
    $dbName = $_POST['db_name'] ?? 'task_management_system';
    $dbUser = $_POST['db_user'] ?? 'root';
    $dbPass = $_POST['db_pass'] ?? '';
    
    try {
        // اختبار الاتصال بقاعدة البيانات
        $pdo = new PDO("mysql:host=$dbHost", $dbUser, $dbPass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // إنشاء قاعدة البيانات إذا لم تكن موجودة
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $success[] = "تم إنشاء قاعدة البيانات بنجاح";
        
        // الاتصال بقاعدة البيانات المحددة
        $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
        
        // قراءة وتنفيذ ملف SQL
        $sqlFile = 'config/init_database.sql';
        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);
            
            // تقسيم الاستعلامات
            $statements = array_filter(array_map('trim', explode(';', $sql)));
            
            foreach ($statements as $statement) {
                if (!empty($statement) && !preg_match('/^(--|CREATE DATABASE|USE)/i', $statement)) {
                    $pdo->exec($statement);
                }
            }
            
            $success[] = "تم إنشاء الجداول والبيانات التجريبية بنجاح";
        }
        
        // إنشاء مجلد الرفع
        if (!is_dir('uploads')) {
            mkdir('uploads', 0755, true);
            $success[] = "تم إنشاء مجلد الرفع";
        }
        
        // إنشاء ملف .htaccess للحماية
        if (!file_exists('uploads/.htaccess')) {
            $htaccessContent = file_get_contents('uploads/.htaccess');
            if ($htaccessContent) {
                $success[] = "تم إنشاء ملف الحماية";
            }
        }
        
        $setupComplete = true;
        
    } catch (PDOException $e) {
        $errors[] = "خطأ في قاعدة البيانات: " . $e->getMessage();
    } catch (Exception $e) {
        $errors[] = "خطأ عام: " . $e->getMessage();
    }
}

// التحقق من متطلبات النظام
$requirements = [
    'PHP Version >= 8.0' => version_compare(PHP_VERSION, '8.0.0', '>='),
    'PDO Extension' => extension_loaded('pdo'),
    'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
    'JSON Extension' => extension_loaded('json'),
    'Session Support' => function_exists('session_start'),
    'File Upload Support' => ini_get('file_uploads'),
    'Uploads Directory Writable' => is_writable(dirname(__FILE__)) || is_writable('uploads')
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تهيئة نظام إدارة المهام</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .setup-container {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
        }
        
        .setup-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .setup-header h1 {
            color: #1f2937;
            font-size: 1.875rem;
            margin-bottom: 0.5rem;
        }
        
        .setup-header p {
            color: #6b7280;
        }
        
        .requirements {
            margin-bottom: 2rem;
        }
        
        .requirement {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: #f9fafb;
            border-radius: 8px;
        }
        
        .requirement.success {
            background: #ecfdf5;
            color: #065f46;
        }
        
        .requirement.error {
            background: #fef2f2;
            color: #991b1b;
        }
        
        .status-icon {
            font-size: 1.25rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            width: 100%;
            padding: 0.875rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #ecfdf5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .alert-error {
            background: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        
        .success-message {
            text-align: center;
            padding: 2rem;
        }
        
        .success-icon {
            font-size: 4rem;
            color: #10b981;
            margin-bottom: 1rem;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
</head>
<body>
    <div class="setup-container">
        <?php if ($setupComplete): ?>
            <div class="success-message">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h2>تم إعداد النظام بنجاح! 🎉</h2>
                <p>يمكنك الآن استخدام النظام</p>
                <div style="margin-top: 2rem;">
                    <a href="index.php" class="btn" style="text-decoration: none; display: inline-block;">
                        الانتقال إلى صفحة تسجيل الدخول
                    </a>
                </div>
            </div>
        <?php else: ?>
            <div class="setup-header">
                <h1>تهيئة نظام إدارة المهام</h1>
                <p>مرحباً بك! دعنا نقوم بإعداد النظام</p>
            </div>

            <!-- عرض الرسائل -->
            <?php foreach ($errors as $error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endforeach; ?>

            <?php foreach ($success as $msg): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($msg); ?>
                </div>
            <?php endforeach; ?>

            <!-- فحص المتطلبات -->
            <div class="requirements">
                <h3 style="margin-bottom: 1rem;">فحص متطلبات النظام</h3>
                <?php foreach ($requirements as $name => $status): ?>
                    <div class="requirement <?php echo $status ? 'success' : 'error'; ?>">
                        <span><?php echo $name; ?></span>
                        <span class="status-icon">
                            <?php echo $status ? '✅' : '❌'; ?>
                        </span>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- نموذج الإعداد -->
            <?php if (array_reduce($requirements, function($carry, $item) { return $carry && $item; }, true)): ?>
                <form method="POST">
                    <h3 style="margin-bottom: 1rem;">إعدادات قاعدة البيانات</h3>
                    
                    <div class="form-group">
                        <label class="form-label">خادم قاعدة البيانات</label>
                        <input type="text" name="db_host" class="form-input" value="localhost" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">اسم قاعدة البيانات</label>
                        <input type="text" name="db_name" class="form-input" value="task_management_system" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">اسم المستخدم</label>
                        <input type="text" name="db_user" class="form-input" value="root" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">كلمة المرور</label>
                        <input type="password" name="db_pass" class="form-input" placeholder="اتركه فارغاً إذا لم تكن هناك كلمة مرور">
                    </div>
                    
                    <button type="submit" class="btn">
                        <i class="fas fa-cog"></i>
                        بدء التهيئة
                    </button>
                </form>
            <?php else: ?>
                <div class="alert alert-error">
                    <strong>تحذير:</strong> يجب تلبية جميع المتطلبات قبل المتابعة
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</body>
</html>
