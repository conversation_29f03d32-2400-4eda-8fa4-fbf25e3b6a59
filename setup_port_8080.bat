@echo off
echo ========================================
echo    إعداد Apache للعمل على المنفذ 8080
echo    Setup Apache to work on port 8080
echo ========================================
echo.

REM التحقق من وجود XAMPP
if exist "C:\xampp\apache\conf\httpd.conf" (
    echo تم العثور على ملف إعدادات Apache
    echo Found Apache configuration file
    echo.
    
    REM إنشاء نسخة احتياطية
    echo إنشاء نسخة احتياطية من الإعدادات...
    echo Creating backup of configuration...
    copy "C:\xampp\apache\conf\httpd.conf" "C:\xampp\apache\conf\httpd.conf.backup" >nul
    
    REM تعديل المنفذ في ملف الإعدادات
    echo تعديل إعدادات المنفذ...
    echo Modifying port settings...
    
    REM استخدام PowerShell لتعديل الملف
    powershell -Command "(Get-Content 'C:\xampp\apache\conf\httpd.conf') -replace 'Listen 80', 'Listen 8080' | Set-Content 'C:\xampp\apache\conf\httpd.conf'"
    powershell -Command "(Get-Content 'C:\xampp\apache\conf\httpd.conf') -replace 'ServerName localhost:80', 'ServerName localhost:8080' | Set-Content 'C:\xampp\apache\conf\httpd.conf'"
    
    echo.
    echo ========================================
    echo تم تعديل إعدادات Apache بنجاح!
    echo Apache settings modified successfully!
    echo ========================================
    echo.
    echo المنفذ الجديد: 8080
    echo New port: 8080
    echo.
    echo يرجى إعادة تشغيل Apache من لوحة تحكم XAMPP
    echo Please restart Apache from XAMPP Control Panel
    echo.
    
    REM فتح لوحة تحكم XAMPP
    echo فتح لوحة تحكم XAMPP...
    echo Opening XAMPP Control Panel...
    start "" "C:\xampp\xampp-control.exe"
    
    echo.
    echo بعد إعادة تشغيل Apache، يمكنك الوصول للموقع عبر:
    echo After restarting Apache, you can access the site via:
    echo http://localhost:8080/Task/
    echo.
    
) else (
    echo خطأ: لم يتم العثور على ملف إعدادات Apache
    echo Error: Apache configuration file not found
    echo.
    echo تأكد من تثبيت XAMPP في المسار الافتراضي:
    echo Make sure XAMPP is installed in the default path:
    echo C:\xampp\
    echo.
)

echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause >nul
