@echo off
echo ========================================
echo    نظام إدارة المهام للشركات
echo    Task Management System
echo ========================================
echo.

echo جاري بدء تشغيل النظام...
echo Starting the system...
echo.

REM التحقق من وجود XAMPP
if exist "C:\xampp\xampp-control.exe" (
    echo تم العثور على XAMPP
    echo Found XAMPP
    
    REM بدء تشغيل XAMPP
    echo بدء تشغيل Apache و MySQL...
    echo Starting Apache and MySQL...
    
    start "" "C:\xampp\xampp-control.exe"
    
    REM انتظار قصير
    timeout /t 3 /nobreak >nul
    
    echo.
    echo ========================================
    echo النظام جاهز للاستخدام!
    echo System is ready to use!
    echo ========================================
    echo.
    echo الروابط المهمة:
    echo Important Links:
    echo.
    echo 1. تهيئة النظام (أول مرة):
    echo    Setup (First time):
    echo    http://localhost/Task/setup.php
    echo.
    echo 2. تسجيل الدخول:
    echo    Login:
    echo    http://localhost/Task/
    echo.
    echo 3. إدارة قاعدة البيانات:
    echo    Database Management:
    echo    http://localhost/phpmyadmin/
    echo.
    echo ========================================
    echo الحسابات التجريبية:
    echo Demo Accounts:
    echo ========================================
    echo المدير:     admin / password123
    echo Admin:      admin / password123
    echo.
    echo المشرف:     supervisor1 / password123  
    echo Supervisor: supervisor1 / password123
    echo.
    echo الموظف:     user1 / password123
    echo Employee:   user1 / password123
    echo ========================================
    echo.
    
    REM فتح المتصفح تلقائياً
    echo فتح المتصفح...
    echo Opening browser...
    timeout /t 2 /nobreak >nul
    start "" "http://localhost/Task/setup.php"
    
) else (
    echo خطأ: لم يتم العثور على XAMPP
    echo Error: XAMPP not found
    echo.
    echo يرجى تثبيت XAMPP أولاً من:
    echo Please install XAMPP first from:
    echo https://www.apachefriends.org/download.html
    echo.
    echo أو تحديث مسار XAMPP في هذا الملف
    echo Or update XAMPP path in this file
)

echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause >nul
