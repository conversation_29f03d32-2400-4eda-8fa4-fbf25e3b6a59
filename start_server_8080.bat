@echo off
echo ========================================
echo    تشغيل خادم PHP على المنفذ 8080
echo    Starting PHP Server on port 8080
echo ========================================
echo.

REM التحقق من وجود PHP
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: PHP غير مثبت أو غير موجود في PATH
    echo Error: PHP is not installed or not in PATH
    echo.
    echo يرجى تثبيت PHP أو إضافته إلى متغير PATH
    echo Please install PHP or add it to PATH variable
    echo.
    echo أو استخدم XAMPP بدلاً من ذلك
    echo Or use XAMPP instead
    echo.
    pause
    exit /b 1
)

echo تم العثور على PHP
echo PHP found
php --version
echo.

REM التحقق من وجود مجلد المشروع
if not exist "%~dp0index.php" (
    echo خطأ: لم يتم العثور على ملفات المشروع
    echo Error: Project files not found
    echo.
    echo تأكد من وجود ملف index.php في نفس مجلد هذا الملف
    echo Make sure index.php exists in the same folder as this file
    echo.
    pause
    exit /b 1
)

echo بدء تشغيل خادم PHP...
echo Starting PHP server...
echo.

echo ========================================
echo الخادم يعمل الآن على:
echo Server is now running on:
echo http://localhost:8080
echo ========================================
echo.

echo للوصول للموقع:
echo To access the site:
echo http://localhost:8080/
echo.

echo لإيقاف الخادم اضغط Ctrl+C
echo To stop the server press Ctrl+C
echo.

REM فتح المتصفح تلقائياً
timeout /t 2 /nobreak >nul
start "" "http://localhost:8080/"

REM بدء خادم PHP
php -S localhost:8080 -t "%~dp0"
