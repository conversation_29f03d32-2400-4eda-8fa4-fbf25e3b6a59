<?php
/**
 * ملف اختبار بسيط
 * Simple Test File
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار النظام</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; direction: rtl; }";
echo "h1 { color: #667eea; }";
echo ".success { color: green; }";
echo ".error { color: red; }";
echo ".info { color: blue; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🧪 اختبار النظام - System Test</h1>";
echo "<hr>";

// اختبار PHP
echo "<h2>✅ PHP يعمل بشكل صحيح</h2>";
echo "<p class='success'>إصدار PHP: " . PHP_VERSION . "</p>";
echo "<p class='info'>الوقت الحالي: " . date('Y-m-d H:i:s') . "</p>";

// اختبار الملفات
echo "<h2>📁 فحص الملفات الأساسية</h2>";

$files = [
    'config/database.php' => 'ملف إعدادات قاعدة البيانات',
    'includes/functions.php' => 'ملف الدوال المساعدة',
    'assets/css/login.css' => 'ملف تصميم تسجيل الدخول',
    'assets/js/login.js' => 'ملف JavaScript'
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ $description: موجود</p>";
    } else {
        echo "<p class='error'>❌ $description: مفقود</p>";
    }
}

// اختبار قاعدة البيانات
echo "<h2>🗄️ اختبار قاعدة البيانات</h2>";

try {
    // محاولة الاتصال بقاعدة البيانات
    $pdo = new PDO('mysql:host=localhost;dbname=task_management_system;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ الاتصال بقاعدة البيانات ناجح</p>";
    
    // فحص الجداول
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "<p class='info'>📊 الجداول الموجودة: " . implode(', ', $tables) . "</p>";
    
} catch (PDOException $e) {
    echo "<p class='error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    echo "<p class='info'>💡 تأكد من تشغيل MySQL وإنشاء قاعدة البيانات</p>";
}

// روابط مفيدة
echo "<h2>🔗 روابط مفيدة</h2>";
echo "<ul>";
echo "<li><a href='debug.php'>صفحة الفحص التفصيلي</a></li>";
echo "<li><a href='setup.php'>إعداد النظام</a></li>";
echo "<li><a href='update_database.php'>تحديث قاعدة البيانات</a></li>";
echo "<li><a href='index.php'>الصفحة الرئيسية</a></li>";
echo "</ul>";

// معلومات الخادم
echo "<h2>🖥️ معلومات الخادم</h2>";
echo "<p>خادم الويب: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد') . "</p>";
echo "<p>المنفذ: " . ($_SERVER['SERVER_PORT'] ?? 'غير محدد') . "</p>";
echo "<p>المسار: " . __DIR__ . "</p>";

echo "</body>";
echo "</html>";
?>
