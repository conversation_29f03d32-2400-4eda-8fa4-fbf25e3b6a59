<?php
/**
 * تحديث قاعدة البيانات
 * Database Update Script
 */

require_once 'config/database.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<h2>تحديث قاعدة البيانات</h2>";
    echo "<p>جاري فحص وتحديث هيكل قاعدة البيانات...</p>";
    
    // فحص وإنشاء الجداول المفقودة
    $tables = [
        'remember_tokens' => "
            CREATE TABLE IF NOT EXISTS remember_tokens (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                token_hash VARCHAR(64) NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_token_hash (token_hash),
                INDEX idx_expires_at (expires_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'failed_logins' => "
            CREATE TABLE IF NOT EXISTS failed_logins (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(100),
                ip_address VARCHAR(45),
                attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_ip_time (ip_address, attempted_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'activity_logs' => "
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT,
                action VARCHAR(100) NOT NULL,
                details TEXT,
                ip_address VARCHAR(45),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                INDEX idx_user_action (user_id, action),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
    ];
    
    foreach ($tables as $tableName => $createSQL) {
        try {
            $conn->exec($createSQL);
            echo "<p style='color: green;'>✓ تم فحص/إنشاء جدول: $tableName</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ خطأ في جدول $tableName: " . $e->getMessage() . "</p>";
        }
    }
    
    // تحديث كلمات المرور إذا لزم الأمر
    $passwordUpdate = "
        UPDATE users 
        SET password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' 
        WHERE password NOT LIKE '$2y$%'
    ";
    
    try {
        $stmt = $conn->prepare($passwordUpdate);
        $stmt->execute();
        $updated = $stmt->rowCount();
        if ($updated > 0) {
            echo "<p style='color: blue;'>✓ تم تحديث $updated كلمة مرور</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>✗ خطأ في تحديث كلمات المرور: " . $e->getMessage() . "</p>";
    }
    
    // إضافة بيانات تجريبية إذا لم تكن موجودة
    $userCheck = $conn->query("SELECT COUNT(*) FROM users")->fetchColumn();
    
    if ($userCheck == 0) {
        $insertUsers = "
            INSERT INTO users (username, email, password, full_name, role, department) VALUES
            ('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin', 'الإدارة'),
            ('supervisor1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'أحمد محمد - مشرف', 'supervisor', 'تقنية المعلومات'),
            ('user1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'سارة أحمد - موظف', 'user', 'تقنية المعلومات'),
            ('user2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'محمد علي - موظف', 'user', 'المحاسبة')
        ";
        
        try {
            $conn->exec($insertUsers);
            echo "<p style='color: green;'>✓ تم إضافة المستخدمين التجريبيين</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ خطأ في إضافة المستخدمين: " . $e->getMessage() . "</p>";
        }
    }
    
    // إضافة مهام تجريبية
    $taskCheck = $conn->query("SELECT COUNT(*) FROM tasks")->fetchColumn();
    
    if ($taskCheck == 0) {
        $insertTasks = "
            INSERT INTO tasks (title, description, assigned_to, assigned_by, supervisor_id, priority, status, due_date) VALUES
            ('تطوير نظام إدارة المخزون', 'تطوير نظام شامل لإدارة المخزون مع واجهة مستخدم حديثة', 3, 1, 2, 'high', 'in_progress', '2024-02-15'),
            ('مراجعة التقارير المالية', 'مراجعة وتدقيق التقارير المالية للربع الأول', 4, 1, 2, 'medium', 'pending', '2024-02-10'),
            ('تحديث موقع الشركة', 'تحديث تصميم وإضافة ميزات جديدة لموقع الشركة', 3, 1, 2, 'medium', 'completed', '2024-01-30'),
            ('إعداد نسخة احتياطية', 'إعداد نظام النسخ الاحتياطي التلقائي للخوادم', 3, 1, 2, 'urgent', 'pending', '2024-02-05')
        ";
        
        try {
            $conn->exec($insertTasks);
            echo "<p style='color: green;'>✓ تم إضافة المهام التجريبية</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ خطأ في إضافة المهام: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<hr>";
    echo "<h3 style='color: green;'>تم تحديث قاعدة البيانات بنجاح! ✅</h3>";
    echo "<p><a href='index.php'>الانتقال إلى صفحة تسجيل الدخول</a></p>";
    echo "<p><a href='admin/dashboard.php'>الانتقال إلى لوحة التحكم</a></p>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>خطأ في الاتصال بقاعدة البيانات:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p>تأكد من:</p>";
    echo "<ul>";
    echo "<li>تشغيل خادم MySQL</li>";
    echo "<li>صحة إعدادات قاعدة البيانات في config/database.php</li>";
    echo "<li>وجود قاعدة البيانات task_management_system</li>";
    echo "</ul>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث قاعدة البيانات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h2 {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        p {
            line-height: 1.6;
            margin: 10px 0;
        }
        
        a {
            color: #667eea;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        ul {
            margin: 10px 0;
            padding-right: 20px;
        }
        
        li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى يتم عرضه بواسطة PHP أعلاه -->
    </div>
</body>
</html>
